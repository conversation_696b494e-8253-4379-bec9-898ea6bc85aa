 




<%@page language="java" %>

<%
// Check if user is logged in
if (session.getAttribute("loggedIn") == null || !(Boolean)session.getAttribute("loggedIn")) {
    response.sendRedirect("login.html");
    return;
}
String username = (String) session.getAttribute("username");
%>

<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
body {
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  background-image: url(./images/book.jpg);
  background-repeat: no-repeat;
  background-size: cover;
}

.topnav {
  overflow: hidden;
  background-color: #333;
}

.topnav a {
  float: left;
  color: #f2f2f2;
  text-align: center;
  padding: 14px 16px;
  text-decoration: none;
  font-size: 17px;
}

.topnav a:hover {
  background-color: #ddd;
  color: black;
}

.topnav a.active {
  background-color: #04AA6D;
  color: white;
}
h2{
    text-align: center;
    color: #fff;
}
p{
    text-align: center;
    color: #fff;
}
</style>
</head>
<body>

<div class="topnav">
  <a class="active" href="index.html">Home</a>
   <a href="bookentry.html">Books Entry</a>

          <a href="bookupdate.html">Book Update</a>     

          <a href="displaybook.html">Book Info</a>  
          <a href="bookdelete.html">Delete Book</a>
          <a href="logout.jsp">Logout</a>
</div>

<div style="padding-left:16px">
  <h2>WELCOME <%= username != null ? username.toUpperCase() : "USER" %></h2>
  <p>Online Library Management System</p>
  <p>Manage your library books efficiently</p>
</div>

</body>
</html>
