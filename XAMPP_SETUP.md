# XAMPP Setup Guide for Online Library Management System

## Prerequisites
- XAMPP installed on your system
- Java Development Kit (JDK) 8 or higher
- Web browser

## Step 1: Start XAMPP Services

1. **Open XAMPP Control Panel**
2. **Start the following services:**
   - ✅ **Apache** (for web server)
   - ✅ **MySQL** (for database)

3. **Verify services are running:**
   - Apache should show "Running" in green
   - MySQL should show "Running" in green

## Step 2: Create Database

### Option A: Using phpMyAdmin (Recommended)
1. Open your browser and go to: `http://localhost/phpmyadmin`
2. Click on "New" in the left sidebar
3. Enter database name: `library_management`
4. Click "Create"
5. Select the new database
6. Click on "Import" tab
7. Choose the `database_setup.sql` file
8. Click "Go" to execute

### Option B: Using MySQL Command Line
1. Open Command Prompt/Terminal
2. Navigate to XAMPP MySQL bin directory:
   ```bash
   # Windows
   cd C:\xampp\mysql\bin
   
   # Mac
   cd /Applications/XAMPP/xamppfiles/bin
   ```
3. Connect to MySQL:
   ```bash
   mysql -u root -p
   # Press Enter when prompted for password (default is empty)
   ```
4. Create database and import:
   ```sql
   CREATE DATABASE library_management;
   USE library_management;
   source path/to/database_setup.sql;
   ```

## Step 3: Verify Database Setup

1. Go to phpMyAdmin: `http://localhost/phpmyadmin`
2. Select `library_management` database
3. Verify these tables exist:
   - `users`
   - `books` 
   - `customers`
4. Check that tables have data (especially the admin user)

## Step 4: Deploy Application

### For Tomcat (if using with XAMPP)
1. Copy the entire `online library management` folder to Tomcat's `webapps` directory
2. Rename it to `online_library_management` (remove spaces)
3. Start Tomcat server
4. Access: `http://localhost:8080/online_library_management`

### For Apache (using XAMPP's Apache)
1. Copy the `web` folder contents to `C:\xampp\htdocs\library`
2. Copy Java classes to appropriate location or use a Java web server
3. Access: `http://localhost/library`

## Step 5: Test Database Connection

1. Navigate to: `http://localhost:8080/online_library_management/test-db.jsp`
   (or appropriate URL based on your setup)
2. This page will show:
   - ✅ Database connection status
   - ✅ Table existence
   - ✅ Current data in tables
   - ❌ Any configuration issues

## Common XAMPP Issues and Solutions

### Issue 1: MySQL Won't Start
**Solutions:**
- Check if port 3306 is already in use
- In XAMPP Control Panel, click "Config" → "my.ini" and change port if needed
- Restart XAMPP as Administrator

### Issue 2: Apache Won't Start
**Solutions:**
- Check if port 80 is already in use (Skype, IIS, etc.)
- Change Apache port in XAMPP Config
- Run XAMPP as Administrator

### Issue 3: Database Connection Failed
**Solutions:**
1. **Verify MySQL is running in XAMPP**
2. **Check database name:** Should be `library_management`
3. **Verify credentials:** Username: `root`, Password: (empty)
4. **Test connection in phpMyAdmin first**

### Issue 4: Tables Don't Exist
**Solutions:**
1. Import `database_setup.sql` via phpMyAdmin
2. Or manually run SQL commands:
   ```sql
   CREATE DATABASE library_management;
   USE library_management;
   -- Then copy/paste contents of database_setup.sql
   ```

## XAMPP Default Settings

- **MySQL Port:** 3306
- **Apache Port:** 80 (HTTP), 443 (HTTPS)
- **MySQL Username:** root
- **MySQL Password:** (empty by default)
- **phpMyAdmin URL:** http://localhost/phpmyadmin
- **Web Root:** C:\xampp\htdocs (Windows) or /Applications/XAMPP/htdocs (Mac)

## Security Note for Production

⚠️ **Important:** XAMPP default settings are for development only!

For production use:
1. Set a strong MySQL root password
2. Remove test databases
3. Disable unnecessary services
4. Configure proper firewall rules

## Testing Checklist

- [ ] XAMPP Control Panel shows Apache and MySQL as "Running"
- [ ] phpMyAdmin accessible at http://localhost/phpmyadmin
- [ ] Database `library_management` exists
- [ ] Tables `users`, `books`, `customers` exist with data
- [ ] Application accessible via web browser
- [ ] Database test page shows successful connection
- [ ] Registration form works without errors

## Troubleshooting Commands

```bash
# Check if MySQL is running (Windows)
netstat -an | findstr :3306

# Check if Apache is running (Windows)
netstat -an | findstr :80

# Restart XAMPP services
# Use XAMPP Control Panel or:
net stop xampp
net start xampp
```

## Alternative Database Configuration

If you're using a different MySQL setup with XAMPP, update `DatabaseConfig.java`:

```java
// For custom MySQL password
private static final String DB_PASSWORD = "your_password_here";

// For different port
private static final String DB_URL = "***********************************************************************************************************";

// For older MySQL versions
private static final String DB_DRIVER = "com.mysql.jdbc.Driver";
```

## Next Steps

1. Start XAMPP services
2. Create database using phpMyAdmin
3. Test database connection
4. Try user registration
5. If issues persist, check the troubleshooting guide
