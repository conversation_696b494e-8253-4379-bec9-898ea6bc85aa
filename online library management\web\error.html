
<!DOCTYPE html>
<html>
<head>
    <title>Error - Library Management System</title>
    <style>
        * {
            transition: all 0.6s;
        }

        html {
            height: 100%;
        }

        body {
            font-family: 'Lato', sans-serif;
            color: #888;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #main {
            display: table;
            width: 100%;
            height: 100vh;
            text-align: center;
        }

        .error-container {
            display: table-cell;
            vertical-align: middle;
        }

        .error-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px;
            margin: 0 auto;
        }

        .error-box h1 {
            font-size: 50px;
            color: #e74c3c;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .error-box h3 {
            color: #555;
            margin-bottom: 30px;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 14px 20px;
            margin: 8px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            transition: all 0.3s;
        }

        .btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div id="main">
        <div class="error-container">
            <div class="error-box">
                <h1>Oops!</h1>
                <h3>Something went wrong</h3>

                <div class="error-message" id="errorMessage">
                    An error occurred while processing your request.
                </div>

                <div>
                    <a href="login.html" class="btn">Go to Login</a>
                    <a href="index.html" class="btn btn-secondary">Go to Home</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get error message from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const errorMsg = urlParams.get('msg');

        if (errorMsg) {
            document.getElementById('errorMessage').textContent = decodeURIComponent(errorMsg);
        }
    </script>
</body>
</html>
