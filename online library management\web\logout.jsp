<%@ page language="java" %>

<!DOCTYPE html>
<html>
<head>
    <title>Logout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .message {
            background-color: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin: 20px auto;
            max-width: 400px;
            border: 1px solid #c3e6cb;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 14px 20px;
            margin: 8px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
        }
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <%
    // Invalidate the session
    if (session != null) {
        session.invalidate();
    }
    %>
    
    <div class="message">
        <h2>Logout Successful</h2>
        <p>You have been successfully logged out from the Library Management System.</p>
        <p>Thank you for using our service!</p>
    </div>
    
    <a href="index.html" class="btn">Go to Home</a>
    <a href="login.html" class="btn">Login Again</a>
</body>
</html>
