
<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
* {
  box-sizing: border-box;
}
.container{width:50%;
          margin-left: 25%;
}

h2{
    text-align: center;
}
input[type=text], select, textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

label {
  padding: 12px 12px 12px 0;
  display: inline-block;
}

input[type=submit] {
  background-color: #04AA6D;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 50%;
}

input[type=reset] {
  background-color: #04AA6D;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 1%;
}
input[type=submit]:hover {
  background-color: #45a049;
}
input[type=reset]:hover {
  background-color: #45a049;
}
.container {
  border-radius: 5px;
  background-color: #f2f2f2;
  padding: 20px;
}

.col-25 {
  float: left;
  width: 25%;
  margin-top: 6px;
}

.col-75 {
  float: left;
  width: 75%;
  margin-top: 6px;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

/* Responsive layout - when the screen is less than 600px wide, make the two columns stack on top of each other instead of next to each other */
@media screen and (max-width: 600px) {
  .col-25, .col-75, input[type=submit] {
    width: 100%;
    margin-top: 0;
  }
}
@media screen and (max-width: 600px) {
  .col-25, .col-75, input[type=reset] {
    width: 100%;
    margin-top: 0;
  }
}
</style>
</head>
<body>

<h2>PLEASE ENTER BOOK ID TO DISPLAY BOOK</h2>

<div class="container">
  <form action="displaybook.jsp" methode="post">
   <div class="row">
      <div class="col-25">
        <label for="id">Book Id</label>
      </div>
      <div class="col-75">
        <input type="text" id="bid" name="id" placeholder="Book Id..">
      </div>
    </div> 
      <br
    <div class="row">
      <input type="submit" value="Display">
      <input type="reset" value="Reset">
    </div>
      
  </form>
</div>

</body>
</html>
