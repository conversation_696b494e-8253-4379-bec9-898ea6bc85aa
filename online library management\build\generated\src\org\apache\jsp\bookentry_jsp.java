package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.sql.*;

public final class bookentry_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final JspFactory _jspxFactory = JspFactory.getDefaultFactory();

  private static java.util.List<String> _jspx_dependants;

  private org.glassfish.jsp.api.ResourceInjector _jspx_resourceInjector;

  public java.util.List<String> getDependants() {
    return _jspx_dependants;
  }

  public void _jspService(HttpServletRequest request, HttpServletResponse response)
        throws java.io.IOException, ServletException {

    PageContext pageContext = null;
    HttpSession session = null;
    ServletContext application = null;
    ServletConfig config = null;
    JspWriter out = null;
    Object page = this;
    JspWriter _jspx_out = null;
    PageContext _jspx_page_context = null;

    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;
      _jspx_resourceInjector = (org.glassfish.jsp.api.ResourceInjector) application.getAttribute("com.sun.appserv.jsp.resource.injector");

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
 

    Connection con;

    PreparedStatement ps;
    ResultSet rs;
    
   String name=request.getParameter("name");

   String author=request.getParameter("author");

   String subject=request.getParameter("subject");
   
   String nob=request.getParameter("nob");
   
   String price=request.getParameter("price");
  

   

   Class.forName("com.mysql.jdbc.Driver");  
 con=DriverManager.getConnection(  
"*******************************","root","briztech"); 
 
   ps=con.prepareStatement("select * from books where id=?");
  rs=ps.executeQuery();
  if(rs.next())
  {
  out.println("Bookid Already present");
  }
  else
  {
    ps=con.prepareStatement("insert into books(name,author,subject,nob,price) values(?,?,?,?,?)");
   
   ps.setString(1,name);

   ps.setString(2,author);

   ps.setString(3,subject);
  
   ps.executeQuery();
   out.println("Book Inserted Successfully");   
  }
   
   
   
      out.write("\n");
      out.write("  \n");
      out.write("   \n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (Throwable t) {
      if (!(t instanceof SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          out.clearBuffer();
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
