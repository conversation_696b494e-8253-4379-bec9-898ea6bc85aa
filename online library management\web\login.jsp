
<%@page language="java" import="java.sql.*"%>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>Login Processing</title>
</head>
<body>
    <%
    Connection con = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        String name = request.getParameter("name");
        String pass = request.getParameter("pass");

        // Input validation
        if (name == null || pass == null || name.trim().isEmpty() || pass.trim().isEmpty()) {
            response.sendRedirect("error.html?msg=Invalid credentials");
            return;
        }

        // Get database connection
        con = DatabaseConfig.getConnection();

        // Use prepared statement to prevent SQL injection
        ps = con.prepareStatement("SELECT * FROM users WHERE username=? AND password=?");
        ps.setString(1, name.trim());
        ps.setString(2, pass.trim());

        rs = ps.executeQuery();

        if (rs.next()) {
            // Set session attributes
            session.setAttribute("username", name);
            session.setAttribute("loggedIn", true);
            response.sendRedirect("welcome.jsp");
        } else {
            response.sendRedirect("error.html?msg=Invalid username or password");
        }

    } catch (Exception e) {
        e.printStackTrace();
        response.sendRedirect("error.html?msg=Database error occurred");
    } finally {
        DatabaseConfig.closeResources(con, ps, rs);
    }
    %>
</body>
</html>
