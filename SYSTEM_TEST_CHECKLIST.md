# 🧪 System Testing Checklist - Online Library Management System

## 📋 **Pre-Testing Setup Verification**

### ✅ **Step 1: XAMPP Services Check**
- [ ] XAMPP Control Panel is open
- [ ] Apache service shows "Running" (green)
- [ ] MySQL service shows "Running" (green)
- [ ] No error messages in XAMPP logs

### ✅ **Step 2: Database Verification**
- [ ] Visit: http://localhost/phpmyadmin
- [ ] Database `library_management` exists
- [ ] Tables exist: `users`, `books`, `customers`
- [ ] Admin user exists in `users` table

### ✅ **Step 3: Application Server Check**
- [ ] Tomcat/GlassFish server is running
- [ ] Application is deployed correctly
- [ ] No compilation errors in server logs

## 🔍 **System Testing Steps**

### **Test 1: Basic Connectivity**
1. **Open browser and navigate to:**
   - Main URL: `http://localhost:8080/online_library_management/`
   - Or: `http://localhost:8080/library_management/`

2. **Expected Result:** ✅ Homepage loads without errors

3. **If fails:** ❌ Check deployment and server status

### **Test 2: Database Connection Test**
1. **Navigate to:** `http://localhost:8080/online_library_management/xampp-check.jsp`

2. **Expected Results:**
   - ✅ Apache is running
   - ✅ phpMyAdmin is accessible
   - ✅ MySQL port 3306 is open
   - ✅ Database connection successful
   - ✅ Database 'library_management' exists

3. **If any fail:** ❌ Follow the solutions provided on the page

### **Test 3: Detailed Database Test**
1. **Navigate to:** `http://localhost:8080/online_library_management/test-db.jsp`

2. **Expected Results:**
   - ✅ Database connection successful
   - ✅ Users table exists with correct structure
   - ✅ Books table exists
   - ✅ Shows existing users count
   - ✅ Displays sample data

3. **If fails:** ❌ Import database_setup.sql via phpMyAdmin

### **Test 4: User Registration**
1. **Navigate to:** `http://localhost:8080/online_library_management/newuser.html`

2. **Fill registration form with test data:**
   - Full Name: `Test User`
   - Username: `testuser123`
   - Email: `<EMAIL>`
   - Phone: `1234567890`
   - Password: `testpass123`
   - Confirm Password: `testpass123`
   - City: `Test City`
   - Address: `Test Address`
   - Gender: Select Male/Female

3. **Click "Register"**

4. **Expected Result:** ✅ "Registration Successful!" message appears

5. **If fails:** ❌ Check error message and database connection

### **Test 5: User Login**
1. **Navigate to:** `http://localhost:8080/online_library_management/login.html`

2. **Test with admin credentials:**
   - Username: `admin`
   - Password: `admin123`

3. **Click "Login"**

4. **Expected Result:** ✅ Redirects to welcome page with personalized greeting

5. **Test with new user:**
   - Username: `testuser123`
   - Password: `testpass123`

6. **Expected Result:** ✅ Successful login and welcome page

### **Test 6: Book Management - Add Book**
1. **After login, navigate to:** Book Entry section

2. **Fill book form:**
   - Book Name: `Test Book`
   - Author: `Test Author`
   - Subject: `Test Subject`
   - Number of Books: `5`
   - Price: `299.99`

3. **Click "Add Book"**

4. **Expected Result:** ✅ "Book added successfully!" message

### **Test 7: Book Management - View Books**
1. **Navigate to:** Display Books section

2. **Expected Result:** ✅ Shows list of books including the test book added

### **Test 8: Book Management - Update Book**
1. **Navigate to:** Book Update section

2. **Update the test book:**
   - Enter book ID (from display books)
   - Modify book details
   - Click "Update"

3. **Expected Result:** ✅ "Book updated successfully!" message

### **Test 9: Book Management - Delete Book**
1. **Navigate to:** Delete Book section

2. **Delete the test book:**
   - Enter book ID
   - Click "Delete"

3. **Expected Result:** ✅ "Book deleted successfully!" message

### **Test 10: Session Management**
1. **Click "Logout"**

2. **Expected Result:** ✅ Redirects to logout page with success message

3. **Try accessing protected pages without login**

4. **Expected Result:** ✅ Redirects to login page

### **Test 11: Input Validation**
1. **Test registration with invalid data:**
   - Empty fields
   - Invalid email format
   - Mismatched passwords
   - Invalid phone number

2. **Expected Result:** ✅ Appropriate error messages displayed

3. **Test book entry with invalid data:**
   - Empty required fields
   - Negative numbers
   - Invalid price

4. **Expected Result:** ✅ Client-side validation prevents submission

### **Test 12: Security Tests**
1. **Test SQL injection prevention:**
   - Try entering `'; DROP TABLE users; --` in login fields
   - Try special characters in form fields

2. **Expected Result:** ✅ No database errors, input is safely handled

3. **Test duplicate username registration:**
   - Try registering with existing username

4. **Expected Result:** ✅ "Username already exists" error message

## 📊 **Test Results Summary**

### ✅ **All Tests Passed - System is Working Properly**
If all tests above pass, your system is fully functional and ready for use.

### ❌ **Some Tests Failed - Troubleshooting Needed**

**Common Issues and Solutions:**

1. **Database Connection Failed:**
   - Start XAMPP MySQL service
   - Import database_setup.sql
   - Check DatabaseConfig.java password

2. **Pages Not Loading (404):**
   - Check server deployment
   - Verify context path
   - Restart application server

3. **Registration Not Working:**
   - Check database table structure
   - Verify form field names match JSP parameters
   - Check server logs for errors

4. **Login Issues:**
   - Verify admin user exists in database
   - Check password matching logic
   - Test with phpMyAdmin direct query

## 🔧 **Quick Diagnostic Commands**

### Check Database Content:
```sql
-- In phpMyAdmin, run these queries:
SELECT * FROM users;
SELECT * FROM books;
DESCRIBE users;
DESCRIBE books;
```

### Check Server Status:
```bash
# Windows - Check if ports are in use
netstat -an | findstr :8080
netstat -an | findstr :3306

# Check Tomcat processes
tasklist | findstr java
```

## 📞 **If You Need Help**

1. **Run the diagnostic pages first:**
   - xampp-check.jsp
   - test-db.jsp

2. **Check server logs:**
   - Tomcat: logs/catalina.out
   - GlassFish: domains/domain1/logs/server.log

3. **Verify file structure:**
   - Ensure all JSP/HTML files are in correct locations
   - Check WEB-INF/lib/ contains MySQL connector JAR

4. **Test step by step:**
   - Start with basic connectivity
   - Then database connection
   - Finally application features

## 🎯 **Success Criteria**

Your system is working properly if:
- ✅ All diagnostic pages show green checkmarks
- ✅ User registration and login work
- ✅ Book management (CRUD) operations work
- ✅ Session management functions correctly
- ✅ Input validation prevents invalid data
- ✅ Security measures are in place
