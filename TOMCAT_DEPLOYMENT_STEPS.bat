@echo off
echo ========================================
echo   TOMCAT DEPLOYMENT SCRIPT
echo ========================================
echo.
echo This script will copy your library management files to Tomcat
echo.
echo BEFORE RUNNING:
echo 1. Make sure Tomcat is installed
echo 2. Make sure Tomcat service is running
echo 3. Close any browsers accessing the application
echo.
pause

echo.
echo Creating application directory...
REM Try both Tomcat 8.5 and 9.0 paths
if exist "C:\Program Files\Apache Software Foundation\Tomcat 8.5\" (
    set TOMCAT_PATH=C:\Program Files\Apache Software Foundation\Tomcat 8.5
    echo Found Tomcat 8.5
) else if exist "C:\Program Files\Apache Software Foundation\Tomcat 9.0\" (
    set TOMCAT_PATH=C:\Program Files\Apache Software Foundation\Tomcat 9.0
    echo Found Tomcat 9.0
) else (
    echo ERROR: Tomcat installation not found!
    echo Please check if <PERSON><PERSON> is installed in Program Files
    pause
    exit /b 1
)

echo Using Tomcat path: %TOMCAT_PATH%
echo.

mkdir "%TOMCAT_PATH%\webapps\library_management" 2>nul

echo.
echo Copying web files...
xcopy "web\*" "%TOMCAT_PATH%\webapps\library_management\" /E /Y

echo.
echo Creating WEB-INF directories...
mkdir "%TOMCAT_PATH%\webapps\library_management\WEB-INF" 2>nul
mkdir "%TOMCAT_PATH%\webapps\library_management\WEB-INF\classes" 2>nul
mkdir "%TOMCAT_PATH%\webapps\library_management\WEB-INF\lib" 2>nul

echo.
echo Copying Java classes...
if exist "build\web\WEB-INF\classes\*" (
    xcopy "build\web\WEB-INF\classes\*" "%TOMCAT_PATH%\webapps\library_management\WEB-INF\classes\" /E /Y
) else (
    echo No compiled classes found. You may need to compile your Java files first.
)

echo.
echo Copying MySQL connector...
if exist "web\WEB-INF\lib\mysql-connector-java-*.jar" (
    copy "web\WEB-INF\lib\mysql-connector-java-*.jar" "%TOMCAT_PATH%\webapps\library_management\WEB-INF\lib\"
) else (
    echo MySQL connector not found in web\WEB-INF\lib\
    echo You may need to download it separately
)

echo.
echo Copying web.xml...
if exist "web\WEB-INF\web.xml" (
    copy "web\WEB-INF\web.xml" "%TOMCAT_PATH%\webapps\library_management\WEB-INF\"
) else (
    echo web.xml not found. Creating basic web.xml...
    echo ^<?xml version="1.0" encoding="UTF-8"?^> > "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo ^<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee" >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo          xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo          http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd" >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo          version="4.0"^> >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo     ^<display-name^>Library Management System^</display-name^> >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
    echo ^</web-app^> >> "%TOMCAT_PATH%\webapps\library_management\WEB-INF\web.xml"
)

echo.
echo ========================================
echo   DEPLOYMENT COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Wait 10-15 seconds for Tomcat to deploy the application
echo 2. Open browser and go to: http://localhost:8080/library_management/
echo 3. Test the registration form
echo.
echo If you get errors:
echo - Check Tomcat logs in: C:\Program Files\Apache Software Foundation\Tomcat 9.0\logs\
echo - Make sure XAMPP MySQL is still running
echo - Check that Java classes are compiled
echo.
pause
