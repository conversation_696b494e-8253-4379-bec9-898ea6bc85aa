@echo off
echo ========================================
echo   TOMCAT DEPLOYMENT SCRIPT
echo ========================================
echo.
echo This script will copy your library management files to Tomcat
echo.
echo BEFORE RUNNING:
echo 1. Make sure Tomcat is installed
echo 2. Make sure Tomcat service is running
echo 3. Close any browsers accessing the application
echo.
pause

echo.
echo Creating application directory...
mkdir "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management" 2>nul

echo.
echo Copying web files...
xcopy "web\*" "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\" /E /Y

echo.
echo Creating WEB-INF directories...
mkdir "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF" 2>nul
mkdir "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\classes" 2>nul
mkdir "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\lib" 2>nul

echo.
echo Copying Java classes...
if exist "build\web\WEB-INF\classes\*" (
    xcopy "build\web\WEB-INF\classes\*" "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\classes\" /E /Y
) else (
    echo No compiled classes found. You may need to compile your Java files first.
)

echo.
echo Copying MySQL connector...
if exist "web\WEB-INF\lib\mysql-connector-java-*.jar" (
    copy "web\WEB-INF\lib\mysql-connector-java-*.jar" "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\lib\"
) else (
    echo MySQL connector not found in web\WEB-INF\lib\
    echo You may need to download it separately
)

echo.
echo Copying web.xml...
if exist "web\WEB-INF\web.xml" (
    copy "web\WEB-INF\web.xml" "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\"
) else (
    echo web.xml not found. Creating basic web.xml...
    echo ^<?xml version="1.0" encoding="UTF-8"?^> > "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo ^<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee" >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo          xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo          http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd" >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo          version="4.0"^> >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo     ^<display-name^>Library Management System^</display-name^> >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
    echo ^</web-app^> >> "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\web.xml"
)

echo.
echo ========================================
echo   DEPLOYMENT COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Wait 10-15 seconds for Tomcat to deploy the application
echo 2. Open browser and go to: http://localhost:8080/library_management/
echo 3. Test the registration form
echo.
echo If you get errors:
echo - Check Tomcat logs in: C:\Program Files\Apache Software Foundation\Tomcat 9.0\logs\
echo - Make sure XAMPP MySQL is still running
echo - Check that Java classes are compiled
echo.
pause
