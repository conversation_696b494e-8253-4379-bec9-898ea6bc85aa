import java.sql.*;

/**
 * Database connection test class
 * Tests the database connectivity using DatabaseConfig
 */
public class dbtest {
    public static void main(String args[]) {
        try {
            // Test database connection using DatabaseConfig
            Connection con = DatabaseConfig.getConnection();

            if (con != null && !con.isClosed()) {
                System.out.println("Database connected successfully!");

                // Test basic query
                PreparedStatement ps = con.prepareStatement("SELECT 1 as test");
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    System.out.println("Database query test passed!");
                }

                // Close resources
                DatabaseConfig.closeResources(con, ps, rs);
                System.out.println("Database connection closed properly.");
            } else {
                System.out.println("Failed to establish database connection!");
            }

        } catch (Exception e) {
            System.err.println("Database connection error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}