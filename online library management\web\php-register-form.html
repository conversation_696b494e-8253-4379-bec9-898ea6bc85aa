<!DOCTYPE html>
<html>
<head>
    <title>PHP Registration Test Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="email"], input[type="password"], input[type="tel"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, input[type="tel"]:focus {
            border-color: #9b59b6;
            outline: none;
        }
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 5px;
        }
        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            cursor: pointer;
        }
        .radio-group input[type="radio"] {
            margin-right: 8px;
            width: auto;
        }
        .btn {
            background: linear-gradient(135deg, #71b7e6, #9b59b6);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-top: 20px;
            font-weight: 500;
            letter-spacing: 1px;
        }
        .btn:hover {
            background: linear-gradient(-135deg, #71b7e6, #9b59b6);
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🧪 PHP Registration Test</h2>
        
        <div class="info">
            <strong>🔧 Testing with PHP (XAMPP Compatible)</strong><br>
            • This form submits to newuser.php instead of newuser.jsp<br>
            • Works with XAMPP Apache (no Tomcat needed)<br>
            • Tests if your database and form processing work<br>
            • Pre-filled with test data for quick testing
        </div>
        
        <div class="warning">
            <strong>⚠️ Before submitting:</strong><br>
            1. Make sure XAMPP Apache and MySQL are running<br>
            2. Database 'library_management' should exist<br>
            3. This will test if the problem is JSP server or database
        </div>
        
        <form action="newuser.php" method="post" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" value="PHP Test User" required>
            </div>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="phptest123" required>
            </div>
            
            <div class="form-group">
                <label for="emailid">Email:</label>
                <input type="email" id="emailid" name="emailid" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="mno">Phone Number (10 digits):</label>
                <input type="tel" id="mno" name="mno" value="9876543210" pattern="[0-9]{10}" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="phptest123" required>
            </div>
            
            <div class="form-group">
                <label for="cpassword">Confirm Password:</label>
                <input type="password" id="cpassword" name="cpassword" value="phptest123" required>
            </div>
            
            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" name="city" value="PHP Test City" required>
            </div>
            
            <div class="form-group">
                <label for="address">Address:</label>
                <input type="text" id="address" name="address" value="PHP Test Address 123" required>
            </div>
            
            <div class="form-group">
                <label>Gender:</label>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="gender" value="male" checked>
                        Male
                    </label>
                    <label>
                        <input type="radio" name="gender" value="female">
                        Female
                    </label>
                </div>
            </div>
            
            <button type="submit" class="btn">
                🧪 Test PHP Registration
            </button>
        </form>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h4>🎯 What This Test Will Show:</h4>
            <p><strong>✅ If PHP registration works:</strong></p>
            <ul>
                <li>Your database setup is correct</li>
                <li>XAMPP MySQL is working</li>
                <li>Form processing logic is correct</li>
                <li>The issue is JSP server configuration</li>
            </ul>
            
            <p><strong>❌ If PHP registration fails:</strong></p>
            <ul>
                <li>Database setup needs to be fixed</li>
                <li>XAMPP MySQL is not running</li>
                <li>Database tables don't exist</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="newuser.html" style="color: #9b59b6; text-decoration: none;">← Back to JSP Registration</a>
        </div>
    </div>
    
    <script>
    function validateForm() {
        var password = document.getElementById("password").value;
        var cpassword = document.getElementById("cpassword").value;
        var email = document.getElementById("emailid").value;
        var phone = document.getElementById("mno").value;

        if (password !== cpassword) {
            alert("Passwords do not match!");
            return false;
        }

        if (password.length < 6) {
            alert("Password must be at least 6 characters long!");
            return false;
        }

        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            alert("Please enter a valid email address!");
            return false;
        }

        var phoneRegex = /^[0-9]{10}$/;
        if (!phoneRegex.test(phone)) {
            alert("Please enter a valid 10-digit phone number!");
            return false;
        }

        return true;
    }
    </script>
</body>
</html>
