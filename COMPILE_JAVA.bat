@echo off
echo ========================================
echo   JAVA COMPILATION SCRIPT
echo ========================================
echo.
echo This script will compile your Java files for Tomcat
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java JDK 8 or higher
    echo Download from: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo Java found. Compiling classes...
echo.

REM Create output directory
mkdir "build\classes" 2>nul

REM Set classpath for compilation
set CLASSPATH=web\WEB-INF\lib\*;%CLASSPATH%

REM Compile Java files
echo Compiling DatabaseConfig.java...
javac -cp "%CLASSPATH%" -d "build\classes" "src\java\DatabaseConfig.java"

if %errorlevel% neq 0 (
    echo ERROR: Compilation failed for DatabaseConfig.java
    echo Check for syntax errors in the Java file
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo.

REM Copy compiled classes to Tomcat
echo Copying compiled classes to Tomcat...
xcopy "build\classes\*" "C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps\library_management\WEB-INF\classes\" /E /Y

echo.
echo ========================================
echo   COMPILATION COMPLETE!
echo ========================================
echo.
echo Compiled classes have been copied to Tomcat
echo.
echo Next steps:
echo 1. Make sure Tomcat is running
echo 2. Wait 10-15 seconds for auto-deployment
echo 3. Test: http://localhost:8080/library_management/
echo.
pause
