

<%@page language="java" import="java.sql.*"%>
<!DOCTYPE html>
<html>
    
    <body>
      <%
          String id=request.getParameter("id");
          String name=request.getParameter("name");
          String author=request.getParameter("author");
          String subject=request.getParameter("subject");
          String nob=request.getParameter("nob");
          String price=request.getParameter("price");
          
          
Connection con;
PreparedStatement ps;
ResultSet rs;
Class.forName("com.mysql.jdbc.Driver");  
 con=DriverManager.getConnection(  
"*******************************","root","briztech");  

ps=con.prepareStatement("select * from books where id=?");
ps.setString(1,id);
rs=ps.executeQuery();

if(rs.next())
{
ps=con.prepareStatement("update books set name=?, author=? ,subject=? ,nob=? ,price=? where id=?");

ps.setString(1,name);
ps.setString(2,author);
ps.setString(3,subject);
ps.setString(4,nob);
ps.setString(5,price);
ps.setString(6,id);
ps.execute();
out.println("Book Successfully Updated");
  
}
else
{
out.println("ID is INVALID");
}
          %>
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>