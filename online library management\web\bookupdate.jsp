

<%@page language="java" import="java.sql.*"%>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>Book Update</title>
    <style>
        .message {
            padding: 20px;
            margin: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <%
    // Check if user is logged in
    if (session.getAttribute("loggedIn") == null || !(Boolean)session.getAttribute("loggedIn")) {
        response.sendRedirect("login.html");
        return;
    }

    Connection con = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        String id = request.getParameter("id");
        String name = request.getParameter("name");
        String author = request.getParameter("author");
        String subject = request.getParameter("subject");
        String nob = request.getParameter("nob");
        String price = request.getParameter("price");

        // Input validation
        if (id == null || name == null || author == null || subject == null ||
            nob == null || price == null || id.trim().isEmpty() ||
            name.trim().isEmpty() || author.trim().isEmpty()) {
            out.println("<div class='message error'>All fields are required!</div>");
        } else {
            // Get database connection
            con = DatabaseConfig.getConnection();

            // Check if book exists
            ps = con.prepareStatement("SELECT * FROM books WHERE id=?");
            ps.setString(1, id.trim());
            rs = ps.executeQuery();

            if (rs.next()) {
                // Update book
                ps = con.prepareStatement("UPDATE books SET name=?, author=?, subject=?, nob=?, price=? WHERE id=?");
                ps.setString(1, name.trim());
                ps.setString(2, author.trim());
                ps.setString(3, subject.trim());
                ps.setString(4, nob.trim());
                ps.setString(5, price.trim());
                ps.setString(6, id.trim());

                int result = ps.executeUpdate();
                if (result > 0) {
                    out.println("<div class='message success'>Book successfully updated!</div>");
                } else {
                    out.println("<div class='message error'>Failed to update book. Please try again.</div>");
                }
            } else {
                out.println("<div class='message error'>Book ID is invalid!</div>");
            }
        }

    } catch (Exception e) {
        e.printStackTrace();
        out.println("<div class='message error'>Database error occurred: " + e.getMessage() + "</div>");
    } finally {
        DatabaseConfig.closeResources(con, ps, rs);
    }
    %>
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>