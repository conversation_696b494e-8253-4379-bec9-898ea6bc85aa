<%@ page language="java" import="java.sql.*, java.net.*, java.io.*" %>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>🧪 System Test - Library Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        .test-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-result {
            font-size: 16px;
            margin: 5px 0;
        }
        .overall-status {
            text-align: center;
            padding: 30px;
            margin: 20px 0;
            border-radius: 15px;
            font-size: 24px;
            font-weight: bold;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            display: inline-block;
            transition: all 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-icon {
            font-size: 20px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🧪 System Functionality Test
        </h1>
        
        <%
        int totalTests = 0;
        int passedTests = 0;
        StringBuilder testResults = new StringBuilder();
        
        // Test 1: Database Connection
        totalTests++;
        testResults.append("<div class='test-section");
        try {
            Connection con = DatabaseConfig.getConnection();
            if (con != null && !con.isClosed()) {
                testResults.append(" success'>");
                testResults.append("<div class='test-header'><span class='status-icon'>✅</span>Test 1: Database Connection</div>");
                testResults.append("<div class='test-result'>✓ Successfully connected to MySQL database</div>");
                testResults.append("<div class='test-result'>✓ DatabaseConfig.java is working correctly</div>");
                passedTests++;
                con.close();
            } else {
                testResults.append(" error'>");
                testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 1: Database Connection</div>");
                testResults.append("<div class='test-result'>✗ Failed to establish database connection</div>");
            }
        } catch (Exception e) {
            testResults.append(" error'>");
            testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 1: Database Connection</div>");
            testResults.append("<div class='test-result'>✗ Database connection failed: " + e.getMessage() + "</div>");
        }
        testResults.append("</div>");
        
        // Test 2: Database Tables
        totalTests++;
        testResults.append("<div class='test-section");
        try {
            Connection con = DatabaseConfig.getConnection();
            DatabaseMetaData metaData = con.getMetaData();
            
            // Check users table
            ResultSet usersTable = metaData.getTables(null, null, "users", null);
            boolean usersExists = usersTable.next();
            usersTable.close();
            
            // Check books table
            ResultSet booksTable = metaData.getTables(null, null, "books", null);
            boolean booksExists = booksTable.next();
            booksTable.close();
            
            if (usersExists && booksExists) {
                testResults.append(" success'>");
                testResults.append("<div class='test-header'><span class='status-icon'>✅</span>Test 2: Database Tables</div>");
                testResults.append("<div class='test-result'>✓ Users table exists</div>");
                testResults.append("<div class='test-result'>✓ Books table exists</div>");
                passedTests++;
            } else {
                testResults.append(" error'>");
                testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 2: Database Tables</div>");
                testResults.append("<div class='test-result'>✗ Missing required tables</div>");
                testResults.append("<div class='test-result'>Users table: " + (usersExists ? "✓" : "✗") + "</div>");
                testResults.append("<div class='test-result'>Books table: " + (booksExists ? "✓" : "✗") + "</div>");
            }
            con.close();
        } catch (Exception e) {
            testResults.append(" error'>");
            testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 2: Database Tables</div>");
            testResults.append("<div class='test-result'>✗ Error checking tables: " + e.getMessage() + "</div>");
        }
        testResults.append("</div>");
        
        // Test 3: Admin User
        totalTests++;
        testResults.append("<div class='test-section");
        try {
            Connection con = DatabaseConfig.getConnection();
            PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
            ResultSet rs = ps.executeQuery();
            rs.next();
            int adminCount = rs.getInt("count");
            
            if (adminCount > 0) {
                testResults.append(" success'>");
                testResults.append("<div class='test-header'><span class='status-icon'>✅</span>Test 3: Admin User</div>");
                testResults.append("<div class='test-result'>✓ Admin user exists in database</div>");
                testResults.append("<div class='test-result'>✓ Default login credentials are available</div>");
                passedTests++;
            } else {
                testResults.append(" warning'>");
                testResults.append("<div class='test-header'><span class='status-icon'>⚠️</span>Test 3: Admin User</div>");
                testResults.append("<div class='test-result'>⚠ Admin user not found</div>");
                testResults.append("<div class='test-result'>You may need to import database_setup.sql</div>");
            }
            rs.close();
            ps.close();
            con.close();
        } catch (Exception e) {
            testResults.append(" error'>");
            testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 3: Admin User</div>");
            testResults.append("<div class='test-result'>✗ Error checking admin user: " + e.getMessage() + "</div>");
        }
        testResults.append("</div>");
        
        // Test 4: File Structure
        totalTests++;
        testResults.append("<div class='test-section");
        try {
            String contextPath = application.getRealPath("/");
            java.io.File webInfLib = new java.io.File(contextPath + "WEB-INF/lib");
            java.io.File[] jarFiles = webInfLib.listFiles((dir, name) -> name.toLowerCase().contains("mysql"));
            
            boolean mysqlJarExists = jarFiles != null && jarFiles.length > 0;
            
            if (mysqlJarExists) {
                testResults.append(" success'>");
                testResults.append("<div class='test-header'><span class='status-icon'>✅</span>Test 4: File Structure</div>");
                testResults.append("<div class='test-result'>✓ MySQL Connector JAR found in WEB-INF/lib</div>");
                testResults.append("<div class='test-result'>✓ Project structure is correct</div>");
                passedTests++;
            } else {
                testResults.append(" error'>");
                testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 4: File Structure</div>");
                testResults.append("<div class='test-result'>✗ MySQL Connector JAR not found</div>");
                testResults.append("<div class='test-result'>✗ Check WEB-INF/lib directory</div>");
            }
        } catch (Exception e) {
            testResults.append(" error'>");
            testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 4: File Structure</div>");
            testResults.append("<div class='test-result'>✗ Error checking file structure: " + e.getMessage() + "</div>");
        }
        testResults.append("</div>");
        
        // Test 5: Sample Data
        totalTests++;
        testResults.append("<div class='test-section");
        try {
            Connection con = DatabaseConfig.getConnection();
            PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) as userCount FROM users");
            ResultSet rs = ps.executeQuery();
            rs.next();
            int userCount = rs.getInt("userCount");
            
            PreparedStatement ps2 = con.prepareStatement("SELECT COUNT(*) as bookCount FROM books");
            ResultSet rs2 = ps2.executeQuery();
            rs2.next();
            int bookCount = rs2.getInt("bookCount");
            
            if (userCount > 0) {
                testResults.append(" success'>");
                testResults.append("<div class='test-header'><span class='status-icon'>✅</span>Test 5: Sample Data</div>");
                testResults.append("<div class='test-result'>✓ Found " + userCount + " users in database</div>");
                testResults.append("<div class='test-result'>✓ Found " + bookCount + " books in database</div>");
                passedTests++;
            } else {
                testResults.append(" warning'>");
                testResults.append("<div class='test-header'><span class='status-icon'>⚠️</span>Test 5: Sample Data</div>");
                testResults.append("<div class='test-result'>⚠ No users found in database</div>");
                testResults.append("<div class='test-result'>You may need to import sample data</div>");
            }
            
            rs.close();
            ps.close();
            rs2.close();
            ps2.close();
            con.close();
        } catch (Exception e) {
            testResults.append(" error'>");
            testResults.append("<div class='test-header'><span class='status-icon'>❌</span>Test 5: Sample Data</div>");
            testResults.append("<div class='test-result'>✗ Error checking sample data: " + e.getMessage() + "</div>");
        }
        testResults.append("</div>");
        
        // Display overall status
        double successRate = (double) passedTests / totalTests * 100;
        %>
        
        <div class="overall-status <%= successRate >= 80 ? "success" : (successRate >= 60 ? "warning" : "error") %>">
            <% if (successRate >= 80) { %>
                🎉 System Status: EXCELLENT
                <div style="font-size: 16px; margin-top: 10px;">
                    <%= passedTests %>/<%= totalTests %> tests passed (<%= String.format("%.0f", successRate) %>%)
                    <br>Your system is working properly!
                </div>
            <% } else if (successRate >= 60) { %>
                ⚠️ System Status: NEEDS ATTENTION
                <div style="font-size: 16px; margin-top: 10px;">
                    <%= passedTests %>/<%= totalTests %> tests passed (<%= String.format("%.0f", successRate) %>%)
                    <br>Some issues need to be resolved
                </div>
            <% } else { %>
                ❌ System Status: CRITICAL ISSUES
                <div style="font-size: 16px; margin-top: 10px;">
                    <%= passedTests %>/<%= totalTests %> tests passed (<%= String.format("%.0f", successRate) %>%)
                    <br>Major configuration problems detected
                </div>
            <% } %>
        </div>
        
        <%= testResults.toString() %>
        
        <div class="test-section info">
            <div class="test-header">🔧 Next Steps</div>
            <% if (successRate >= 80) { %>
                <div class="test-result">✅ Your system is ready to use!</div>
                <div class="test-result">✅ Try the registration and login features</div>
                <div class="test-result">✅ Test book management operations</div>
            <% } else { %>
                <div class="test-result">1. Fix any failed tests above</div>
                <div class="test-result">2. Import database_setup.sql if needed</div>
                <div class="test-result">3. Check XAMPP services are running</div>
                <div class="test-result">4. Verify MySQL Connector JAR is in place</div>
            <% } %>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="newuser.html" class="btn">Test Registration</a>
            <a href="login.html" class="btn">Test Login</a>
            <a href="xampp-check.jsp" class="btn">XAMPP Check</a>
            <a href="test-db.jsp" class="btn">Database Details</a>
        </div>
        
        <div class="test-section info" style="margin-top: 30px;">
            <div class="test-header">📊 Test Summary</div>
            <table>
                <tr><th>Test Category</th><th>Status</th><th>Description</th></tr>
                <tr><td>Database Connection</td><td><%= passedTests >= 1 ? "✅ PASS" : "❌ FAIL" %></td><td>Basic MySQL connectivity</td></tr>
                <tr><td>Database Tables</td><td><%= passedTests >= 2 ? "✅ PASS" : "❌ FAIL" %></td><td>Required tables exist</td></tr>
                <tr><td>Admin User</td><td><%= passedTests >= 3 ? "✅ PASS" : "⚠️ WARNING" %></td><td>Default login available</td></tr>
                <tr><td>File Structure</td><td><%= passedTests >= 4 ? "✅ PASS" : "❌ FAIL" %></td><td>JAR files in place</td></tr>
                <tr><td>Sample Data</td><td><%= passedTests >= 5 ? "✅ PASS" : "⚠️ WARNING" %></td><td>Database has content</td></tr>
            </table>
        </div>
    </div>
</body>
</html>
