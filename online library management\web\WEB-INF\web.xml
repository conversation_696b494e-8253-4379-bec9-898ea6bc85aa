<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">
    
    <display-name>Online Library Management System</display-name>
    <description>A web-based library management system</description>
    
    <!-- Welcome file list -->
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    
    <!-- Error pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/error.html</location>
    </error-page>
    
    <error-page>
        <error-code>500</error-code>
        <location>/error.html</location>
    </error-page>
    
    <error-page>
        <exception-type>java.lang.Exception</exception-type>
        <location>/error.html</location>
    </error-page>
    
    <!-- Session configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>
    
    <!-- Security constraints for admin pages -->
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Admin Pages</web-resource-name>
            <url-pattern>/welcome.jsp</url-pattern>
            <url-pattern>/bookentry.jsp</url-pattern>
            <url-pattern>/bookupdate.jsp</url-pattern>
            <url-pattern>/bookdelete.jsp</url-pattern>
            <url-pattern>/displaybook.jsp</url-pattern>
        </web-resource-collection>
    </security-constraint>
    
</web-app>
