<!DOCTYPE html>
<html>
<head>
    <title>Debug Registration Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="email"], input[type="password"], input[type="tel"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, input[type="tel"]:focus {
            border-color: #04AA6D;
            outline: none;
        }
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 5px;
        }
        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            cursor: pointer;
        }
        .radio-group input[type="radio"] {
            margin-right: 8px;
            width: auto;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .debug-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-btn {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .test-btn:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔧 Debug Registration Form</h2>
        
        <div class="debug-info">
            <strong>🧪 Debug Mode Active</strong><br>
            • No JavaScript validation (to test server directly)<br>
            • Pre-filled with test data<br>
            • Will show exactly what happens when submitted<br>
            • Check browser Network tab (F12) to see request/response
        </div>
        
        <div class="test-buttons">
            <a href="xampp-check.jsp" class="test-btn">Test XAMPP</a>
            <a href="test-db.jsp" class="test-btn">Test Database</a>
            <a href="system-test.jsp" class="test-btn">System Test</a>
        </div>
        
        <div class="warning">
            <strong>⚠️ Before submitting:</strong><br>
            1. Make sure XAMPP MySQL is running<br>
            2. Check that database 'library_management' exists<br>
            3. Open browser Developer Tools (F12) → Network tab<br>
            4. Watch what happens when you click submit
        </div>
        
        <!-- Form WITHOUT JavaScript validation -->
        <form action="newuser.jsp" method="post">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" value="Debug Test User" required>
            </div>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="debuguser123" required>
            </div>
            
            <div class="form-group">
                <label for="emailid">Email:</label>
                <input type="email" id="emailid" name="emailid" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="mno">Phone Number (10 digits):</label>
                <input type="tel" id="mno" name="mno" value="9876543210" pattern="[0-9]{10}" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="debug123" required>
            </div>
            
            <div class="form-group">
                <label for="cpassword">Confirm Password:</label>
                <input type="password" id="cpassword" name="cpassword" value="debug123" required>
            </div>
            
            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" name="city" value="Debug City" required>
            </div>
            
            <div class="form-group">
                <label for="address">Address:</label>
                <input type="text" id="address" name="address" value="Debug Address 123" required>
            </div>
            
            <div class="form-group">
                <label>Gender:</label>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="gender" value="male" checked>
                        Male
                    </label>
                    <label>
                        <input type="radio" name="gender" value="female">
                        Female
                    </label>
                </div>
            </div>
            
            <button type="submit" class="btn" onclick="console.log('Form submitting...'); return true;">
                🔧 DEBUG SUBMIT (No JS Validation)
            </button>
        </form>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h4>🔍 What to Watch For:</h4>
            <ol>
                <li><strong>Click Submit</strong> → Form should try to load newuser.jsp</li>
                <li><strong>If nothing happens</strong> → Check browser console for errors</li>
                <li><strong>If 405 error</strong> → JSP server not working</li>
                <li><strong>If 404 error</strong> → newuser.jsp file not found</li>
                <li><strong>If database error</strong> → XAMPP MySQL issue</li>
                <li><strong>If success</strong> → Registration working!</li>
            </ol>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #d1ecf1; border-radius: 5px;">
            <h4>📱 How to Debug:</h4>
            <p><strong>1. Open Developer Tools:</strong> Press F12</p>
            <p><strong>2. Go to Network tab</strong></p>
            <p><strong>3. Click "DEBUG SUBMIT"</strong></p>
            <p><strong>4. Watch the Network tab:</strong></p>
            <ul>
                <li>Should show POST request to newuser.jsp</li>
                <li>Check the status code (200=success, 404=not found, 405=method not allowed, 500=server error)</li>
                <li>Click on the request to see response details</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="newuser.html" style="color: #04AA6D; text-decoration: none;">← Back to Main Registration</a>
        </div>
    </div>
    
    <script>
    // Simple logging to help debug
    console.log("Debug registration form loaded");
    
    // Log form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        console.log("Form is being submitted...");
        console.log("Action:", this.action);
        console.log("Method:", this.method);
        
        // Log all form data
        const formData = new FormData(this);
        console.log("Form data:");
        for (let [key, value] of formData.entries()) {
            console.log(key + ": " + value);
        }
    });
    </script>
</body>
</html>
