

<%@ page language="java" import="java.sql.*" %>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>Book Entry</title>
    <style>
        .message {
            padding: 20px;
            margin: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <%
    // Check if user is logged in
    if (session.getAttribute("loggedIn") == null || !(Boolean)session.getAttribute("loggedIn")) {
        response.sendRedirect("login.html");
        return;
    }

    Connection con = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        String name = request.getParameter("name");
        String author = request.getParameter("author");
        String subject = request.getParameter("subject");
        String nob = request.getParameter("nob");
        String price = request.getParameter("price");

        // Input validation
        if (name == null || author == null || subject == null || nob == null || price == null ||
            name.trim().isEmpty() || author.trim().isEmpty() || subject.trim().isEmpty() ||
            nob.trim().isEmpty() || price.trim().isEmpty()) {
            out.println("<div class='message error'>All fields are required!</div>");
        } else {
            // Get database connection
            con = DatabaseConfig.getConnection();

            // Check if book already exists
            ps = con.prepareStatement("SELECT * FROM books WHERE name=?");
            ps.setString(1, name.trim());
            rs = ps.executeQuery();

            if (rs.next()) {
                out.println("<div class='message warning'>Book name already exists!</div>");
            } else {
                // Insert new book
                ps = con.prepareStatement("INSERT INTO books(name, author, subject, nob, price) VALUES (?,?,?,?,?)");
                ps.setString(1, name.trim());
                ps.setString(2, author.trim());
                ps.setString(3, subject.trim());
                ps.setString(4, nob.trim());
                ps.setString(5, price.trim());

                int result = ps.executeUpdate();
                if (result > 0) {
                    out.println("<div class='message success'>Book inserted successfully!</div>");
                } else {
                    out.println("<div class='message error'>Failed to insert book. Please try again.</div>");
                }
            }
        }

    } catch (Exception e) {
        e.printStackTrace();
        out.println("<div class='message error'>Database error occurred: " + e.getMessage() + "</div>");
    } finally {
        DatabaseConfig.closeResources(con, ps, rs);
    }
    %>
  
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>
