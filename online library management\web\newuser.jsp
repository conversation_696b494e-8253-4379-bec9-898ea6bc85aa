



<%@ page language="java" import="java.sql.*" %>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>User Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .message {
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 14px 20px;
            margin: 8px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
        }
        .btn:hover {
            opacity: 0.8;
        }
        h2 {
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>User Registration Result</h2>
        <%
        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement checkPs = null;
        ResultSet rs = null;

        try {
            // Only process if this is a POST request with form data
            if ("POST".equalsIgnoreCase(request.getMethod())) {
                String name = request.getParameter("name");
                String gender = request.getParameter("gender");
                String address = request.getParameter("address");
                String city = request.getParameter("city");
                String mno = request.getParameter("mno");
                String emailid = request.getParameter("emailid");
                String username = request.getParameter("username");
                String password = request.getParameter("password");
                String cpassword = request.getParameter("cpassword");

                // Debug: Print received parameters
                out.println("<!-- Debug: Received parameters -->");
                out.println("<!-- Name: " + name + " -->");
                out.println("<!-- Username: " + username + " -->");
                out.println("<!-- Email: " + emailid + " -->");

                // Input validation
                if (name == null || gender == null || address == null || city == null ||
                    mno == null || emailid == null || username == null || password == null ||
                    name.trim().isEmpty() || username.trim().isEmpty() || password.trim().isEmpty()) {
                    out.println("<div class='message error'>All fields are required!</div>");
                } else if (!password.equals(cpassword)) {
                    out.println("<div class='message error'>Passwords do not match!</div>");
                } else {
                    // Get database connection
                    con = DatabaseConfig.getConnection();
                    out.println("<!-- Debug: Database connection successful -->");

                    // Check if username already exists
                    checkPs = con.prepareStatement("SELECT username FROM users WHERE username=?");
                    checkPs.setString(1, username.trim());
                    rs = checkPs.executeQuery();

                    if (rs.next()) {
                        out.println("<div class='message error'>Username already exists! Please choose a different username.</div>");
                    } else {
                        // Insert new user
                        ps = con.prepareStatement("INSERT INTO users (name, gender, address, city, mno, emailid, username, password) VALUES (?,?,?,?,?,?,?,?)");
                        ps.setString(1, name.trim());
                        ps.setString(2, gender);
                        ps.setString(3, address.trim());
                        ps.setString(4, city.trim());
                        ps.setString(5, mno.trim());
                        ps.setString(6, emailid.trim());
                        ps.setString(7, username.trim());
                        ps.setString(8, password.trim());

                        int result = ps.executeUpdate();
                        if (result > 0) {
                            out.println("<div class='message success'>");
                            out.println("<h3>Registration Successful!</h3>");
                            out.println("<p>Welcome <strong>" + name + "</strong>! Your account has been created successfully.</p>");
                            out.println("<p>You can now login with username: <strong>" + username + "</strong></p>");
                            out.println("</div>");
                        } else {
                            out.println("<div class='message error'>Registration failed. Please try again.</div>");
                        }
                    }
                }
            } else {
                out.println("<div class='message error'>Invalid request method. Please use the registration form.</div>");
            }

        } catch (Exception e) {
            out.println("<!-- Debug: Exception occurred: " + e.getMessage() + " -->");
            e.printStackTrace();
            out.println("<div class='message error'>Database error occurred: " + e.getMessage() + "</div>");
            out.println("<div class='message error'>Please make sure the database is running and properly configured.</div>");
        } finally {
            DatabaseConfig.closeResources(con, ps, rs);
            if (checkPs != null) {
                try { checkPs.close(); } catch (SQLException e) { /* ignore */ }
            }
        }
        %>

        <div style="text-align: center; margin-top: 30px;">
            <a href="login.html" class="btn">Go to Login</a>
            <a href="newuser.html" class="btn" style="background-color: #6c757d;">Register Another User</a>
        </div>
    </div>
</body>
</html>
