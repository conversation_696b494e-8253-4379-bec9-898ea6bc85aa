



<%@ page language="java" import="java.sql.*" %>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>User Registration</title>
    <style>
        .message {
            padding: 20px;
            margin: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <%
    Connection con = null;
    PreparedStatement ps = null;

    try {
        String name = request.getParameter("name");
        String gender = request.getParameter("gender");
        String address = request.getParameter("address");
        String city = request.getParameter("city");
        String mno = request.getParameter("mno");
        String emailid = request.getParameter("emailid");
        String username = request.getParameter("username");
        String password = request.getParameter("password");

        // Input validation
        if (name == null || gender == null || address == null || city == null ||
            mno == null || emailid == null || username == null || password == null ||
            name.trim().isEmpty() || username.trim().isEmpty() || password.trim().isEmpty()) {
            out.println("<div class='message error'>All fields are required!</div>");
        } else {
            // Get database connection
            con = DatabaseConfig.getConnection();

            // Check if username already exists
            PreparedStatement checkPs = con.prepareStatement("SELECT username FROM users WHERE username=?");
            checkPs.setString(1, username.trim());
            ResultSet rs = checkPs.executeQuery();

            if (rs.next()) {
                out.println("<div class='message error'>Username already exists! Please choose a different username.</div>");
            } else {
                // Insert new user
                ps = con.prepareStatement("INSERT INTO users (name, gender, address, city, mno, emailid, username, password) VALUES (?,?,?,?,?,?,?,?)");
                ps.setString(1, name.trim());
                ps.setString(2, gender);
                ps.setString(3, address.trim());
                ps.setString(4, city.trim());
                ps.setString(5, mno.trim());
                ps.setString(6, emailid.trim());
                ps.setString(7, username.trim());
                ps.setString(8, password.trim());

                int result = ps.executeUpdate();
                if (result > 0) {
                    out.println("<div class='message success'><b>User registered successfully!</b></div>");
                } else {
                    out.println("<div class='message error'>Registration failed. Please try again.</div>");
                }
            }

            rs.close();
            checkPs.close();
        }

    } catch (Exception e) {
        e.printStackTrace();
        out.println("<div class='message error'>Database error occurred: " + e.getMessage() + "</div>");
    } finally {
        DatabaseConfig.closeResources(con, ps);
    }
    %>
   <html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="login.html">Login</a>
</body>
</html>
