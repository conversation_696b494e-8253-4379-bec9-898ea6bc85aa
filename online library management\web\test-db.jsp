<%@ page language="java" import="java.sql.*" %>
<%@page import="DatabaseConfig"%>

<!DOCTYPE html>
<html>
<head>
    <title>Database Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Connection Test - XAMPP Configuration</h1>

        <div class='info'>
            <strong>XAMPP Setup Checklist:</strong><br>
            1. ✅ XAMPP Control Panel → Start Apache<br>
            2. ✅ XAMPP Control Panel → Start MySQL<br>
            3. ✅ Visit <a href="http://localhost/phpmyadmin" target="_blank">phpMyAdmin</a><br>
            4. ✅ Create database: library_management<br>
            5. ✅ Import database_setup.sql
        </div>

        <%
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            out.println("<div class='info'><strong>Testing XAMPP MySQL connection...</strong></div>");
            out.println("<div class='info'>Connection URL: **********************************************</div>");
            out.println("<div class='info'>Username: root (XAMPP default)</div>");
            out.println("<div class='info'>Password: (empty - XAMPP default)</div>");
            
            // Test database connection
            con = DatabaseConfig.getConnection();
            
            if (con != null && !con.isClosed()) {
                out.println("<div class='success'><strong>✓ Database connection successful!</strong></div>");
                
                // Test if users table exists
                DatabaseMetaData metaData = con.getMetaData();
                rs = metaData.getTables(null, null, "users", null);
                
                if (rs.next()) {
                    out.println("<div class='success'><strong>✓ Users table exists!</strong></div>");
                    
                    // Show table structure
                    out.println("<h3>Users Table Structure:</h3>");
                    out.println("<table>");
                    out.println("<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th></tr>");
                    
                    ResultSet columns = metaData.getColumns(null, null, "users", null);
                    while (columns.next()) {
                        String columnName = columns.getString("COLUMN_NAME");
                        String dataType = columns.getString("TYPE_NAME");
                        String nullable = columns.getString("IS_NULLABLE");
                        out.println("<tr><td>" + columnName + "</td><td>" + dataType + "</td><td>" + nullable + "</td></tr>");
                    }
                    out.println("</table>");
                    columns.close();
                    
                    // Count existing users
                    ps = con.prepareStatement("SELECT COUNT(*) as user_count FROM users");
                    ResultSet countRs = ps.executeQuery();
                    if (countRs.next()) {
                        int userCount = countRs.getInt("user_count");
                        out.println("<div class='info'><strong>Current users in database: " + userCount + "</strong></div>");
                    }
                    countRs.close();
                    
                    // Show existing users (first 5)
                    ps = con.prepareStatement("SELECT id, name, username, emailid FROM users LIMIT 5");
                    ResultSet usersRs = ps.executeQuery();
                    
                    out.println("<h3>Existing Users (First 5):</h3>");
                    out.println("<table>");
                    out.println("<tr><th>ID</th><th>Name</th><th>Username</th><th>Email</th></tr>");
                    
                    while (usersRs.next()) {
                        out.println("<tr>");
                        out.println("<td>" + usersRs.getInt("id") + "</td>");
                        out.println("<td>" + usersRs.getString("name") + "</td>");
                        out.println("<td>" + usersRs.getString("username") + "</td>");
                        out.println("<td>" + usersRs.getString("emailid") + "</td>");
                        out.println("</tr>");
                    }
                    out.println("</table>");
                    usersRs.close();
                    
                } else {
                    out.println("<div class='error'><strong>✗ Users table does not exist!</strong></div>");
                    out.println("<div class='info'>Please run the database_setup.sql script to create the required tables.</div>");
                }
                
                // Test books table
                rs = metaData.getTables(null, null, "books", null);
                if (rs.next()) {
                    out.println("<div class='success'><strong>✓ Books table exists!</strong></div>");
                } else {
                    out.println("<div class='error'><strong>✗ Books table does not exist!</strong></div>");
                }
                
            } else {
                out.println("<div class='error'><strong>✗ Failed to establish database connection!</strong></div>");
            }
            
        } catch (ClassNotFoundException e) {
            out.println("<div class='error'><strong>✗ MySQL Driver not found!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'><strong>XAMPP Solution:</strong><br>");
            out.println("1. Make sure mysql-connector-java.jar is in WEB-INF/lib/ directory<br>");
            out.println("2. Download from: <a href='https://dev.mysql.com/downloads/connector/j/' target='_blank'>MySQL Connector/J</a><br>");
            out.println("3. Copy JAR file to: online library management/web/WEB-INF/lib/</div>");
        } catch (SQLException e) {
            out.println("<div class='error'><strong>✗ XAMPP MySQL connection failed!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'><strong>XAMPP Troubleshooting:</strong><br>");
            out.println("1. ✅ Open XAMPP Control Panel<br>");
            out.println("2. ✅ Start MySQL service (should show 'Running' in green)<br>");
            out.println("3. ✅ Test phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a><br>");
            out.println("4. ✅ Create database 'library_management' in phpMyAdmin<br>");
            out.println("5. ✅ Import database_setup.sql via phpMyAdmin Import tab<br>");
            out.println("6. ✅ Check if port 3306 is free (netstat -an | findstr :3306)</div>");
        } catch (Exception e) {
            out.println("<div class='error'><strong>✗ Unexpected error occurred!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            e.printStackTrace();
        } finally {
            DatabaseConfig.closeResources(con, ps, rs);
        }
        %>
        
        <div style="margin-top: 30px;">
            <a href="newuser.html" style="background-color: #04AA6D; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Registration Again</a>
            <a href="index.html" style="background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">Go to Home</a>
        </div>
    </div>
</body>
</html>
