# Online Library Management System

A comprehensive web-based library management system built with Java, JSP, and MySQL.

## Features

- **User Management**
  - User registration with validation
  - Secure login/logout functionality
  - Session management

- **Book Management**
  - Add new books to the library
  - Update existing book information
  - Delete books from the system
  - Search and display book details

- **Security Features**
  - SQL injection prevention using prepared statements
  - Input validation and sanitization
  - Session-based authentication
  - Error handling and user feedback

## Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Java, JSP (JavaServer Pages)
- **Database**: MySQL
- **Server**: Apache Tomcat / GlassFish
- **Build Tool**: Apache Ant (NetBeans project)

## Prerequisites

Before running this application, make sure you have:

1. **Java Development Kit (JDK) 8 or higher**
2. **MySQL Server** (5.7 or higher recommended)
3. **Apache Tomcat** or **GlassFish Server**
4. **MySQL Connector/J** (included in project)

## Installation and Setup

### 1. Database Setup

1. Start your MySQL server
2. Run the database setup script:
   ```sql
   mysql -u root -p < database_setup.sql
   ```
   
   Or manually execute the SQL commands in `database_setup.sql`

3. Update database credentials in `DatabaseConfig.java` if needed:
   ```java
   private static final String DB_URL = "**********************************************";
   private static final String DB_USERNAME = "root";
   private static final String DB_PASSWORD = "your_password_here";
   ```

### 2. Project Setup

1. Import the project into your IDE (NetBeans recommended)
2. Ensure MySQL Connector JAR is in the classpath
3. Build the project using your IDE or Ant

### 3. Deployment

1. Deploy the WAR file to your application server
2. Access the application at: `http://localhost:8080/online_library_management/`

## Usage

### For New Users
1. Navigate to the application homepage
2. Click "New User" to register
3. Fill in all required information
4. Login with your credentials

### For Existing Users
1. Use the login form with your username and password
2. Access the dashboard to manage books

### Book Management
- **Add Books**: Use the "Books Entry" section
- **Update Books**: Use the "Book Update" section  
- **Delete Books**: Use the "Delete Book" section
- **View Books**: Use the "Book Info" section

## Default Login Credentials

- **Username**: admin
- **Password**: admin123

## Project Structure

```
online library management/
├── src/java/
│   ├── DatabaseConfig.java    # Database configuration
│   └── dbtest.java            # Database connection test
├── web/
│   ├── WEB-INF/
│   │   ├── web.xml           # Deployment descriptor
│   │   └── lib/              # JAR files
│   ├── css/                  # Stylesheets
│   ├── js/                   # JavaScript files
│   ├── images/               # Image assets
│   ├── *.html               # Static pages
│   └── *.jsp                # Dynamic pages
└── nbproject/               # NetBeans project files
```

## Security Features

- **SQL Injection Prevention**: All database queries use prepared statements
- **Input Validation**: Client-side and server-side validation
- **Session Management**: Secure session handling for user authentication
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL server is running
   - Check database credentials in `DatabaseConfig.java`
   - Ensure database `library_management` exists

2. **ClassNotFoundException for MySQL Driver**
   - Verify MySQL Connector JAR is in WEB-INF/lib/
   - Check project classpath configuration

3. **404 Error on Pages**
   - Verify application is properly deployed
   - Check context path in `glassfish-web.xml`

## License

This project is open source and available under the MIT License.

## Support

For support and questions, please create an issue in the project repository.
