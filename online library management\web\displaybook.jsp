
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
th{
    color: blue;
}
table,td{
    text-align: center;
}
</style>
<body>
<%@page language="java" import="java.sql.*"%>
<%@page import="DatabaseConfig"%>

<%
// Check if user is logged in
if (session.getAttribute("loggedIn") == null || !(Boolean)session.getAttribute("loggedIn")) {
    response.sendRedirect("login.html");
    return;
}

Connection con = null;
PreparedStatement ps = null;
ResultSet rs = null;

try {
    String id = request.getParameter("id");

    // Input validation
    if (id == null || id.trim().isEmpty()) {
        out.println("<div style='text-align:center; color:red; padding:20px;'>Book ID is required!</div>");
    } else {
        // Get database connection
        con = DatabaseConfig.getConnection();

        ps = con.prepareStatement("SELECT * FROM books WHERE id=?");
        ps.setString(1, id.trim());

        rs = ps.executeQuery();

        if (rs.next()) {
            out.println("<center>");
            out.println("<table border='1' style='border-collapse: collapse; margin: 20px auto;'>");
            out.println("<tr>");
            out.println("<th colspan='6' style='background-color: #f2f2f2; padding: 10px;'>");
            out.println("Book Details");
            out.println("</th>");
            out.println("</tr>");
            out.println("<tr style='background-color: #e6f3ff;'>");
            out.println("<th style='padding: 8px;'>ID</th>");
            out.println("<th style='padding: 8px;'>NAME</th>");
            out.println("<th style='padding: 8px;'>AUTHOR</th>");
            out.println("<th style='padding: 8px;'>SUBJECT</th>");
            out.println("<th style='padding: 8px;'>NO. of BOOKS</th>");
            out.println("<th style='padding: 8px;'>PRICE</th>");
            out.println("</tr>");
            out.println("<tr>");
            out.println("<td style='padding: 8px; text-align: center;'>" + rs.getString("id") + "</td>");
            out.println("<td style='padding: 8px; text-align: center;'>" + rs.getString("name") + "</td>");
            out.println("<td style='padding: 8px; text-align: center;'>" + rs.getString("author") + "</td>");
            out.println("<td style='padding: 8px; text-align: center;'>" + rs.getString("subject") + "</td>");
            out.println("<td style='padding: 8px; text-align: center;'>" + rs.getString("nob") + "</td>");
            out.println("<td style='padding: 8px; text-align: center;'>$" + rs.getString("price") + "</td>");
            out.println("</tr>");
            out.println("</table>");
            out.println("</center>");
        } else {
            out.println("<div style='text-align:center; color:red; padding:20px; font-size:18px;'>Book not found!</div>");
        }
    }

} catch (Exception e) {
    e.printStackTrace();
    out.println("<div style='text-align:center; color:red; padding:20px;'>Database error occurred: " + e.getMessage() + "</div>");
} finally {
    DatabaseConfig.closeResources(con, ps, rs);
}
%>
  
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>
