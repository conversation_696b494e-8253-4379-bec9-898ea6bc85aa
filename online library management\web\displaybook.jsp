
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
th{
    color: blue;
}
table,td{
    text-align: center;
}
</style>
<body>
<%@page language="java" import="java.sql.*"%>


        <%
       Connection con;

    PreparedStatement ps;
    ResultSet rs;
    
   
   String id=request.getParameter("id");

   

   Class.forName("com.mysql.jdbc.Driver");  
 con=DriverManager.getConnection(  
"*******************************","root","briztech");  

   ps=con.prepareStatement("select * from books where id=?");
   
     
   ps.setString(1,id);
   
   rs=ps.executeQuery();
 if(rs.next())
 {
  out.println("<center>");
  out.println("<table border=1>");
  out.println("<tr>");
  out.println("<th colspan=6>");
  out.println("Book Details");
  out.println("</th>");
  out.println("</tr>");
  out.println("<tr>");
  out.println("<th>");
  out.println("ID");
  out.println("</th>");
  out.println("<th>");
  out.println("NAME");
  out.println("</th>");
  out.println("<th>");
  out.println("AUTHOR");
  out.println("</th>");
  out.println("<th>");
  out.println("SUBJECT");
  out.println("</th>");
  out.println("<th>");
  out.println("NO. of BOOKS");
  out.println("</th>");
  out.println("<th>");
  out.println("PRICE of BOOK");
  out.println("</th>");
  out.println("</tr>");
  out.println("<tr>");
  out.println("<td>");
  out.println(rs.getString("id"));
  out.println("</td>");
  out.println("<td>");
  out.println(rs.getString("name")); 
  out.println("</td>");
  out.println("<td>");
  out.println(rs.getString("author"));
  out.println("</td>");
  out.println("<td>");
  out.println(rs.getString("subject")); 
  out.println("</td>");
  out.println("<td>");
  out.println(rs.getString("nob"));
  out.println("</td>");
  out.println("<td>");
  out.println(rs.getString("price"));
  out.println("</td>");
  out.println("</tr>");
  out.println("</table>");
  out.println("</center>");
 }
 else
 {
     out.println("Book not Found !!!");
     
 }   
   %>
  
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>
