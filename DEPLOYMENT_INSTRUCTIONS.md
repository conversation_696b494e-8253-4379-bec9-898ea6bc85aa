# 🚀 How to Run the Online Library Management System

## ❌ **Important: Don't use npm!**
This is a **Java web application**, not a Node.js project. npm commands will not work.

## ✅ **Method 1: Using XAMPP + Tomcat (Easiest)**

### Step 1: Install Required Software
1. **XAMPP** - Download from: https://www.apachefriends.org/
2. **Apache Tomcat** - Download from: https://tomcat.apache.org/download-90.cgi
3. **Java JDK 8+** - Download from: https://www.oracle.com/java/technologies/downloads/

### Step 2: Setup XAMPP Database
1. **Start XAMPP Control Panel**
2. **Start Apache and MySQL services**
3. **Open phpMyAdmin:** http://localhost/phpmyadmin
4. **Create database:** `library_management`
5. **Import database:** Upload `database_setup.sql` file

### Step 3: Deploy to Tomcat
1. **Copy project folder** to <PERSON><PERSON>'s `webapps` directory:
   ```
   Copy: C:\Users\<USER>\OneDrive\Desktop\anmol - Copy\online library management
   To: C:\apache-tomcat-9.x.x\webapps\library_management
   ```

2. **Ensure MySQL Connector JAR** is in place:
   ```
   File: webapps\library_management\WEB-INF\lib\mysql-connector-java.jar
   ```

3. **Start Tomcat:**
   - Run `bin\startup.bat` (Windows) or `bin\startup.sh` (Linux/Mac)

4. **Access application:**
   - URL: http://localhost:8080/library_management/

## ✅ **Method 2: Using NetBeans IDE (Recommended for Development)**

### Step 1: Install NetBeans
1. Download NetBeans IDE from: https://netbeans.apache.org/
2. Install with Java Web and EE support

### Step 2: Open Project
1. **File → Open Project**
2. **Navigate to:** `C:\Users\<USER>\OneDrive\Desktop\anmol - Copy\online library management`
3. **Open the project**

### Step 3: Configure Server
1. **Right-click project → Properties**
2. **Run → Server:** Select GlassFish or Tomcat
3. **Apply changes**

### Step 4: Setup Database (Same as Method 1)
1. Start XAMPP MySQL
2. Create database via phpMyAdmin
3. Import database_setup.sql

### Step 5: Run Project
1. **Right-click project → Run** (or press F6)
2. **NetBeans will automatically deploy and open browser**

## ✅ **Method 3: Using Eclipse IDE**

### Step 1: Install Eclipse IDE for Enterprise Java
1. Download from: https://www.eclipse.org/downloads/
2. Choose "Eclipse IDE for Enterprise Java and Web Developers"

### Step 2: Import Project
1. **File → Import → Existing Projects into Workspace**
2. **Browse to:** `C:\Users\<USER>\OneDrive\Desktop\anmol - Copy\online library management`
3. **Import project**

### Step 3: Configure Server
1. **Window → Show View → Servers**
2. **Add new server** (Tomcat 9.x)
3. **Add project to server**

### Step 4: Run Project
1. **Right-click project → Run As → Run on Server**

## 🔧 **Troubleshooting Common Issues**

### Issue 1: "Build Failed" or "Compilation Error"
**Solution:**
- Make sure Java JDK is installed and configured
- Check that all JAR files are in WEB-INF/lib/
- Verify project structure is correct

### Issue 2: "Database Connection Failed"
**Solution:**
1. Start XAMPP MySQL service
2. Check database exists: `library_management`
3. Verify connection settings in `DatabaseConfig.java`
4. Test with: http://localhost:8080/library_management/xampp-check.jsp

### Issue 3: "404 Not Found"
**Solution:**
- Check Tomcat is running on port 8080
- Verify project is deployed correctly
- Check context path in server configuration

### Issue 4: "ClassNotFoundException"
**Solution:**
- Ensure mysql-connector-java.jar is in WEB-INF/lib/
- Check Java classpath configuration
- Restart application server

## 📁 **Project Structure Verification**

Your project should look like this:
```
online library management/
├── src/
│   └── java/
│       ├── DatabaseConfig.java
│       └── dbtest.java
├── web/
│   ├── WEB-INF/
│   │   ├── web.xml
│   │   └── lib/
│   │       └── mysql-connector-java.jar
│   ├── *.html (login.html, newuser.html, etc.)
│   ├── *.jsp (login.jsp, newuser.jsp, etc.)
│   └── css/, js/, images/ folders
├── nbproject/ (NetBeans files)
└── build.xml (Ant build file)
```

## 🎯 **Quick Start Commands**

### For XAMPP Users:
```bash
# 1. Start XAMPP services
# Open XAMPP Control Panel → Start Apache & MySQL

# 2. Create database
# Visit: http://localhost/phpmyadmin
# Create database: library_management
# Import: database_setup.sql

# 3. Copy project to Tomcat webapps
# Start Tomcat
# Visit: http://localhost:8080/library_management/
```

### For IDE Users:
```bash
# 1. Open IDE (NetBeans/Eclipse)
# 2. Import/Open project
# 3. Configure server (Tomcat/GlassFish)
# 4. Setup database (XAMPP MySQL)
# 5. Run project (F6 in NetBeans)
```

## 🔗 **Useful URLs After Setup**

- **Application Home:** http://localhost:8080/library_management/
- **Registration:** http://localhost:8080/library_management/newuser.html
- **Login:** http://localhost:8080/library_management/login.html
- **Database Test:** http://localhost:8080/library_management/test-db.jsp
- **XAMPP Check:** http://localhost:8080/library_management/xampp-check.jsp
- **phpMyAdmin:** http://localhost/phpmyadmin

## 📞 **Need Help?**

1. **Check XAMPP Status:** Visit xampp-check.jsp
2. **Test Database:** Visit test-db.jsp
3. **View Logs:** Check Tomcat logs in `logs/catalina.out`
4. **Verify Setup:** Follow XAMPP_SETUP.md guide

Remember: This is a **Java web application**, not a Node.js project. Use Java development tools, not npm!
