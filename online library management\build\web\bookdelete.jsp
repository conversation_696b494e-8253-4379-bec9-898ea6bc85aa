
<%@page language="java" import="java.sql.*"%>

<html>
    
    <body bgcolor="red">
        
        |<%
            String id=request.getParameter("id");
        
          
Connection con;
PreparedStatement ps;
ResultSet rs;
Class.forName("com.mysql.jdbc.Driver");  
 con=DriverManager.getConnection(  
"*******************************","root","briztech");  

ps=con.prepareStatement("SELECT * FROM books where id=?");
ps.setString(1,id);

rs=ps.executeQuery();
if(rs.next())
{
 ps=con.prepareStatement("DELETE FROM books where id=?");   
 ps.setString(1,id);
 ps.execute();
 out.println("Book Deleted Successfully.");
}
else
{
out.println("ID is INVALID");
}
         %>
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>
