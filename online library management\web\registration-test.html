<!DOCTYPE html>
<html>
<head>
    <title>Registration Test - Simple Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background-color: #f0f8ff;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"], input[type="tel"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        input[type="radio"] {
            margin-right: 5px;
        }
        .gender-group {
            margin: 10px 0;
        }
        .gender-group label {
            display: inline;
            margin-right: 15px;
            font-weight: normal;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🧪 Registration Test Form</h2>
        
        <div class="info">
            <strong>Test Instructions:</strong><br>
            1. Fill out this simple form<br>
            2. Click "Test Registration"<br>
            3. Check if the form submits properly<br>
            4. Look for any error messages
        </div>
        
        <form action="newuser.jsp" method="post" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="testuser123" required>
            </div>
            
            <div class="form-group">
                <label for="emailid">Email:</label>
                <input type="email" id="emailid" name="emailid" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="mno">Phone Number:</label>
                <input type="tel" id="mno" name="mno" value="1234567890" pattern="[0-9]{10}" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="testpass123" required>
            </div>
            
            <div class="form-group">
                <label for="cpassword">Confirm Password:</label>
                <input type="password" id="cpassword" name="cpassword" value="testpass123" required>
            </div>
            
            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" name="city" value="Test City" required>
            </div>
            
            <div class="form-group">
                <label for="address">Address:</label>
                <input type="text" id="address" name="address" value="Test Address" required>
            </div>
            
            <div class="form-group">
                <label>Gender:</label>
                <div class="gender-group">
                    <label><input type="radio" name="gender" value="male" checked> Male</label>
                    <label><input type="radio" name="gender" value="female"> Female</label>
                </div>
            </div>
            
            <button type="submit" class="btn">🧪 Test Registration</button>
        </form>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="newuser.html" style="color: #04AA6D; text-decoration: none;">← Back to Main Registration Form</a>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <strong>Troubleshooting:</strong><br>
            • If you get a 405 error: JSP server is not working<br>
            • If you get a database error: Check XAMPP MySQL<br>
            • If form doesn't submit: Check browser console for errors<br>
            • If page loads but nothing happens: Check server logs
        </div>
    </div>

    <script>
    function validateForm() {
        var password = document.getElementById("password").value;
        var cpassword = document.getElementById("cpassword").value;
        var email = document.getElementById("emailid").value;
        var phone = document.getElementById("mno").value;

        // Check if passwords match
        if (password !== cpassword) {
            alert("Passwords do not match!");
            return false;
        }

        // Check password strength
        if (password.length < 6) {
            alert("Password must be at least 6 characters long!");
            return false;
        }

        // Validate email format
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            alert("Please enter a valid email address!");
            return false;
        }

        // Validate phone number
        var phoneRegex = /^[0-9]{10}$/;
        if (!phoneRegex.test(phone)) {
            alert("Please enter a valid 10-digit phone number!");
            return false;
        }

        console.log("Form validation passed, submitting...");
        return true;
    }
    </script>
</body>
</html>
