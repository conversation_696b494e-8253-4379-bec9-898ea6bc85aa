<%@ page language="java" import="java.sql.*, java.net.*, java.io.*" %>

<!DOCTYPE html>
<html>
<head>
    <title>XAMPP Status Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
        }
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 XAMPP Status Check</h1>
        
        <h2>1. Apache Server Status</h2>
        <%
        try {
            URL url = new URL("http://localhost/");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(3000);
            int responseCode = connection.getResponseCode();
            
            if (responseCode == 200) {
                out.println("<div class='success'>✅ <strong>Apache is running!</strong> (Response: " + responseCode + ")</div>");
            } else {
                out.println("<div class='warning'>⚠️ <strong>Apache responding but with code:</strong> " + responseCode + "</div>");
            }
        } catch (Exception e) {
            out.println("<div class='error'>❌ <strong>Apache is not running!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'>💡 <strong>Solution:</strong> Open XAMPP Control Panel and start Apache</div>");
        }
        %>
        
        <h2>2. phpMyAdmin Access</h2>
        <%
        try {
            URL url = new URL("http://localhost/phpmyadmin/");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(3000);
            int responseCode = connection.getResponseCode();
            
            if (responseCode == 200) {
                out.println("<div class='success'>✅ <strong>phpMyAdmin is accessible!</strong></div>");
                out.println("<div class='info'>🔗 <a href='http://localhost/phpmyadmin' target='_blank'>Open phpMyAdmin</a></div>");
            } else {
                out.println("<div class='warning'>⚠️ <strong>phpMyAdmin responding but with code:</strong> " + responseCode + "</div>");
            }
        } catch (Exception e) {
            out.println("<div class='error'>❌ <strong>phpMyAdmin is not accessible!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'>💡 <strong>Solution:</strong> Make sure Apache is running in XAMPP</div>");
        }
        %>
        
        <h2>3. MySQL Port Check</h2>
        <%
        try {
            Socket socket = new Socket();
            socket.connect(new InetSocketAddress("localhost", 3306), 3000);
            socket.close();
            out.println("<div class='success'>✅ <strong>MySQL port 3306 is open!</strong></div>");
        } catch (Exception e) {
            out.println("<div class='error'>❌ <strong>MySQL port 3306 is not accessible!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'>💡 <strong>Solution:</strong> Open XAMPP Control Panel and start MySQL</div>");
        }
        %>
        
        <h2>4. Database Connection Test</h2>
        <%
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection con = DriverManager.getConnection(
                "*****************************************************************************************", 
                "root", 
                ""
            );
            
            if (con != null && !con.isClosed()) {
                out.println("<div class='success'>✅ <strong>MySQL connection successful!</strong></div>");
                
                // Check if library_management database exists
                PreparedStatement ps = con.prepareStatement("SHOW DATABASES LIKE 'library_management'");
                ResultSet rs = ps.executeQuery();
                
                if (rs.next()) {
                    out.println("<div class='success'>✅ <strong>Database 'library_management' exists!</strong></div>");
                } else {
                    out.println("<div class='warning'>⚠️ <strong>Database 'library_management' does not exist!</strong></div>");
                    out.println("<div class='info'>💡 <strong>Solution:</strong> Create database in phpMyAdmin or run database_setup.sql</div>");
                }
                
                rs.close();
                ps.close();
                con.close();
            }
        } catch (ClassNotFoundException e) {
            out.println("<div class='error'>❌ <strong>MySQL Driver not found!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'>💡 <strong>Solution:</strong> Add mysql-connector-java.jar to WEB-INF/lib/</div>");
        } catch (SQLException e) {
            out.println("<div class='error'>❌ <strong>MySQL connection failed!</strong><br>");
            out.println("Error: " + e.getMessage() + "</div>");
            out.println("<div class='info'>💡 <strong>Solution:</strong> Start MySQL in XAMPP Control Panel</div>");
        }
        %>
        
        <h2>5. Quick Setup Guide</h2>
        <div class='info'>
            <strong>🚀 Quick XAMPP Setup Steps:</strong><br><br>
            <strong>Step 1:</strong> Open XAMPP Control Panel<br>
            <strong>Step 2:</strong> Click "Start" for Apache (should show green "Running")<br>
            <strong>Step 3:</strong> Click "Start" for MySQL (should show green "Running")<br>
            <strong>Step 4:</strong> Open <a href="http://localhost/phpmyadmin" target="_blank">phpMyAdmin</a><br>
            <strong>Step 5:</strong> Create database named "library_management"<br>
            <strong>Step 6:</strong> Import the database_setup.sql file<br>
            <strong>Step 7:</strong> Test registration form
        </div>
        
        <h2>6. Common XAMPP Issues</h2>
        <div class='warning'>
            <strong>⚠️ Port Conflicts:</strong><br>
            • Apache Port 80: Often conflicts with Skype, IIS<br>
            • MySQL Port 3306: May conflict with other MySQL installations<br><br>
            
            <strong>🔧 Solutions:</strong><br>
            • Close conflicting applications<br>
            • Change ports in XAMPP Config<br>
            • Run XAMPP as Administrator
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="test-db.jsp" class="btn">Full Database Test</a>
            <a href="newuser.html" class="btn">Try Registration</a>
            <a href="http://localhost/phpmyadmin" class="btn" target="_blank">Open phpMyAdmin</a>
        </div>
        
        <div class='info' style="margin-top: 20px;">
            <strong>📋 Next Steps:</strong><br>
            1. If all checks pass ✅, try the registration form<br>
            2. If any checks fail ❌, follow the solutions provided<br>
            3. Visit the full database test page for detailed analysis<br>
            4. Check XAMPP_SETUP.md for complete setup instructions
        </div>
    </div>
</body>
</html>
