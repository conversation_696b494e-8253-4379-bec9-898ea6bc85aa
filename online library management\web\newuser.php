<?php
// PHP version of registration for immediate testing with XAMPP
?>
<!DOCTYPE html>
<html>
<head>
    <title>User Registration - PHP Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .message {
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 14px 20px;
            margin: 8px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
        }
        .btn:hover {
            opacity: 0.8;
        }
        h2 {
            text-align: center;
            color: #333;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>User Registration Result - PHP Version</h2>
        
        <div class="info">
            <strong>🔧 This is a PHP version for testing with XAMPP</strong><br>
            Since JSP isn't working, this PHP version will test if your database connection and form processing work.
        </div>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Database configuration for XAMPP
            $servername = "localhost";
            $username = "root";
            $password = "";
            $dbname = "library_management";
            
            try {
                // Create connection
                $conn = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
                $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Get form data
                $name = trim($_POST['name']);
                $gender = $_POST['gender'];
                $address = trim($_POST['address']);
                $city = trim($_POST['city']);
                $mno = trim($_POST['mno']);
                $emailid = trim($_POST['emailid']);
                $user = trim($_POST['username']);
                $pass = trim($_POST['password']);
                $cpass = trim($_POST['cpassword']);
                
                // Validation
                if (empty($name) || empty($user) || empty($pass) || empty($emailid)) {
                    echo "<div class='message error'>All fields are required!</div>";
                } elseif ($pass !== $cpass) {
                    echo "<div class='message error'>Passwords do not match!</div>";
                } elseif (strlen($pass) < 6) {
                    echo "<div class='message error'>Password must be at least 6 characters long!</div>";
                } elseif (!filter_var($emailid, FILTER_VALIDATE_EMAIL)) {
                    echo "<div class='message error'>Invalid email format!</div>";
                } elseif (!preg_match('/^[0-9]{10}$/', $mno)) {
                    echo "<div class='message error'>Phone number must be 10 digits!</div>";
                } else {
                    // Check if username already exists
                    $checkStmt = $conn->prepare("SELECT username FROM users WHERE username = ?");
                    $checkStmt->execute([$user]);
                    
                    if ($checkStmt->rowCount() > 0) {
                        echo "<div class='message error'>Username already exists! Please choose a different username.</div>";
                    } else {
                        // Insert new user
                        $stmt = $conn->prepare("INSERT INTO users (name, gender, address, city, mno, emailid, username, password) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        
                        if ($stmt->execute([$name, $gender, $address, $city, $mno, $emailid, $user, $pass])) {
                            echo "<div class='message success'>";
                            echo "<h3>🎉 Registration Successful!</h3>";
                            echo "<p>Welcome <strong>$name</strong>! Your account has been created successfully.</p>";
                            echo "<p>You can now login with username: <strong>$user</strong></p>";
                            echo "<p><strong>✅ Database connection is working!</strong></p>";
                            echo "<p><strong>✅ Form processing is working!</strong></p>";
                            echo "<p><strong>✅ User registration is working!</strong></p>";
                            echo "</div>";
                        } else {
                            echo "<div class='message error'>Registration failed. Please try again.</div>";
                        }
                    }
                }
                
            } catch(PDOException $e) {
                echo "<div class='message error'>";
                echo "<strong>Database Error:</strong> " . $e->getMessage() . "<br><br>";
                echo "<strong>Common Solutions:</strong><br>";
                echo "1. Make sure XAMPP MySQL is running<br>";
                echo "2. Create database 'library_management' in phpMyAdmin<br>";
                echo "3. Import database_setup.sql<br>";
                echo "4. Check database credentials";
                echo "</div>";
            }
        } else {
            echo "<div class='message error'>Invalid request method. Please use the registration form.</div>";
        }
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="login.html" class="btn">Go to Login</a>
            <a href="newuser.html" class="btn" style="background-color: #6c757d;">Register Another User</a>
            <a href="php-register-form.html" class="btn" style="background-color: #17a2b8;">PHP Test Form</a>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <strong>📋 What This Test Shows:</strong><br>
            • If this PHP version works → Your database and XAMPP setup is correct<br>
            • If this PHP version works → The issue is JSP server configuration<br>
            • If this fails → Database setup needs to be fixed first<br><br>
            
            <strong>🎯 Next Steps:</strong><br>
            1. If PHP registration works → Setup Tomcat for JSP<br>
            2. If PHP registration fails → Fix database setup first<br>
            3. Use NetBeans IDE for easiest JSP setup
        </div>
    </div>
</body>
</html>
