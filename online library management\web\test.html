<!DOCTYPE html>
<html>
<head>
    <title>Server Test - Library Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background-color: #f0f8ff;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success {
            color: #28a745;
            font-size: 24px;
            margin: 20px 0;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            display: inline-block;
        }
        .btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Server Test Successful!</h1>
        <div class="success">✅ Your web server is working correctly!</div>
        
        <p>If you can see this page, your application server is running properly.</p>
        
        <h3>Next Steps:</h3>
        <p>1. Test the main homepage</p>
        <p>2. Check database connectivity</p>
        <p>3. Try the registration form</p>
        
        <div style="margin-top: 30px;">
            <a href="index.html" class="btn">Go to Homepage</a>
            <a href="newuser.html" class="btn">Registration</a>
            <a href="login.html" class="btn">Login</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h4>🔧 If JSP pages show 405 errors:</h4>
            <p>1. Make sure XAMPP MySQL is running</p>
            <p>2. Check that your application server supports JSP</p>
            <p>3. Try these test URLs:</p>
            <ul style="text-align: left;">
                <li><code>xampp-check.jsp</code> - XAMPP status</li>
                <li><code>test-db.jsp</code> - Database test</li>
                <li><code>system-test.jsp</code> - Full system test</li>
            </ul>
        </div>
    </div>
</body>
</html>
