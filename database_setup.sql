-- Library Management System Database Setup Script
-- For XAMPP Users:
-- 1. Start XAMPP Control Panel
-- 2. Start Apache and MySQL services
-- 3. Open phpMyAdmin: http://localhost/phpmyadmin
-- 4. Click "New" to create database
-- 5. Name: library_management
-- 6. Click "Import" tab and select this file
-- 7. Click "Go" to execute
--
-- For Command Line:
-- mysql -u root -p < database_setup.sql

-- Create database
CREATE DATABASE IF NOT EXISTS library_management;
USE library_management;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    gender VARCHAR(10) NOT NULL,
    address VARCHAR(100) NOT NULL,
    city VARCHAR(30) NOT NULL,
    mno VARCHAR(15) NOT NULL,
    emailid VARCHAR(50) NOT NULL UNIQUE,
    username VARCHA<PERSON>(30) NOT NULL UNIQUE,
    password VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create books table
CREATE TABLE IF NOT EXISTS books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    author VARCHAR(50) NOT NULL,
    subject VARCHAR(50) NOT NULL,
    nob INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create customers table (for library members)
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    yname VARCHAR(50) NOT NULL,
    fname VARCHAR(50) NOT NULL,
    rno VARCHAR(10) NOT NULL,
    rate VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample admin user
INSERT INTO users (name, gender, address, city, mno, emailid, username, password) 
VALUES ('Admin User', 'Male', '123 Admin Street', 'Admin City', '1234567890', '<EMAIL>', 'admin', 'admin123')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample books
INSERT INTO books (name, author, subject, nob, price) VALUES
('Java Programming', 'James Gosling', 'Computer Science', 5, 45.99),
('Database Systems', 'Ramez Elmasri', 'Computer Science', 3, 65.50),
('Web Development', 'Jon Duckett', 'Computer Science', 4, 39.99),
('Data Structures', 'Robert Sedgewick', 'Computer Science', 6, 55.75),
('Software Engineering', 'Ian Sommerville', 'Computer Science', 2, 70.25)
ON DUPLICATE KEY UPDATE name = name;

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(emailid);
CREATE INDEX idx_books_name ON books(name);
CREATE INDEX idx_books_author ON books(author);
CREATE INDEX idx_books_subject ON books(subject);

-- Display success message
SELECT 'Database setup completed successfully!' as Status;

-- Show created tables
SHOW TABLES;

-- Show sample data
SELECT 'Sample Users:' as Info;
SELECT id, name, username, emailid FROM users;

SELECT 'Sample Books:' as Info;
SELECT id, name, author, subject, nob, price FROM books;
