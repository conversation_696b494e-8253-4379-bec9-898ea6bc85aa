import java.sql.*;

/**
 * Database configuration class for Library Management System
 * Centralizes database connection management
 */
public class DatabaseConfig {
    
    // Database configuration constants
    private static final String DB_URL = "**********************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = ""; // Update this with your MySQL password
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";
    
    /**
     * Get database connection
     * @return Connection object
     * @throws SQLException if connection fails
     * @throws ClassNotFoundException if driver not found
     */
    public static Connection getConnection() throws SQLException, ClassNotFoundException {
        Class.forName(DB_DRIVER);
        return DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
    }
    
    /**
     * Close database resources safely
     * @param conn Connection to close
     * @param ps PreparedStatement to close
     * @param rs ResultSet to close
     */
    public static void closeResources(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Close database resources safely (without ResultSet)
     * @param conn Connection to close
     * @param ps PreparedStatement to close
     */
    public static void closeResources(Connection conn, PreparedStatement ps) {
        closeResources(conn, ps, null);
    }
    
    /**
     * Test database connection
     * @return true if connection successful, false otherwise
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (Exception e) {
            System.err.println("Database connection test failed: " + e.getMessage());
            return false;
        }
    }
}
