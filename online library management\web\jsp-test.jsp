<%@ page language="java" %>
<!DOCTYPE html>
<html>
<head>
    <title>JSP Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background-color: #f0f8ff;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success {
            color: #28a745;
            font-size: 24px;
            margin: 20px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            background-color: #04AA6D;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 JSP Test Successful!</h1>
        
        <div class="success">✅ Your JSP server is working!</div>
        
        <div class="info">
            <strong>Server Information:</strong><br>
            Server Info: <%= application.getServerInfo() %><br>
            Java Version: <%= System.getProperty("java.version") %><br>
            Current Time: <%= new java.util.Date() %><br>
            Session ID: <%= session.getId() %>
        </div>
        
        <% 
        String testParam = request.getParameter("test");
        if (testParam != null) {
        %>
            <div class="info">
                <strong>✅ Form Parameter Test Passed!</strong><br>
                Received parameter: <%= testParam %>
            </div>
        <% } else { %>
            <div class="info">
                <strong>Test Form Submission:</strong><br>
                <form method="post" action="jsp-test.jsp">
                    <input type="hidden" name="test" value="JSP Form Working">
                    <input type="submit" value="Test Form Submit" class="btn">
                </form>
            </div>
        <% } %>
        
        <div style="margin-top: 30px;">
            <a href="debug-register.html" class="btn">Test Registration</a>
            <a href="newuser.html" class="btn">Main Registration</a>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <strong>✅ If you can see this page, JSP is working correctly!</strong><br>
            This means the registration issue is likely:<br>
            • Database connection problem<br>
            • Form validation error<br>
            • JavaScript blocking submission<br>
            • Missing database tables
        </div>
    </div>
</body>
</html>
