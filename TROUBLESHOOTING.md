# Troubleshooting Guide - Online Library Management System

## Registration Not Working Issue

If users are not getting registered after filling the registration form, follow these steps:

### Step 1: Test Database Connection
1. Navigate to: `http://localhost:8080/online_library_management/test-db.jsp`
2. This will show you the database connection status and any issues

### Step 2: Common Issues and Solutions

#### Issue 1: Database Connection Failed
**Symptoms:** Error messages about database connection
**Solutions:**
1. **Check MySQL Server Status**
   ```bash
   # Windows
   net start mysql
   
   # Linux/Mac
   sudo systemctl start mysql
   ```

2. **Verify Database Exists**
   ```sql
   mysql -u root -p
   SHOW DATABASES;
   ```
   If `library_management` database doesn't exist, run:
   ```sql
   source database_setup.sql;
   ```

3. **Update Database Password**
   - Edit `online library management/src/java/DatabaseConfig.java`
   - Update the `DB_PASSWORD` field with your MySQL root password:
   ```java
   private static final String DB_PASSWORD = "your_mysql_password";
   ```

#### Issue 2: MySQL Driver Not Found
**Symptoms:** ClassNotFoundException for MySQL driver
**Solutions:**
1. Check if `mysql-connector-java.jar` exists in `web/WEB-INF/lib/`
2. If missing, download from: https://dev.mysql.com/downloads/connector/j/
3. Copy the JAR file to `web/WEB-INF/lib/` directory
4. Restart your application server

#### Issue 3: Users Table Doesn't Exist
**Symptoms:** Table 'library_management.users' doesn't exist
**Solutions:**
1. Run the database setup script:
   ```bash
   mysql -u root -p library_management < database_setup.sql
   ```
2. Or manually create the database and tables using the SQL commands in `database_setup.sql`

#### Issue 4: Form Data Not Reaching JSP
**Symptoms:** Registration form submits but no processing occurs
**Solutions:**
1. Check if the form method is POST in `newuser.html`:
   ```html
   <form action="newuser.jsp" method="post">
   ```
2. Verify all form field names match the JSP parameter names
3. Check browser developer tools for any JavaScript errors

#### Issue 5: Server Configuration Issues
**Symptoms:** 404 errors or pages not loading
**Solutions:**
1. **For Tomcat:**
   - Ensure WAR file is deployed correctly
   - Check `webapps` directory for the application folder
   - Verify context path in server configuration

2. **For GlassFish:**
   - Check application deployment status in admin console
   - Verify context root settings

### Step 3: Debug Mode
Enable debug mode by adding this to your JSP files temporarily:
```jsp
<%
// Debug information
out.println("<!-- Debug: Request Method: " + request.getMethod() + " -->");
out.println("<!-- Debug: Context Path: " + request.getContextPath() + " -->");
out.println("<!-- Debug: Request URI: " + request.getRequestURI() + " -->");
%>
```

### Step 4: Check Server Logs
1. **Tomcat Logs:** `logs/catalina.out` or `logs/localhost.log`
2. **GlassFish Logs:** `domains/domain1/logs/server.log`
3. Look for any error messages or stack traces

### Step 5: Manual Database Test
Test database connectivity manually:
```sql
mysql -u root -p
USE library_management;
SELECT * FROM users;
INSERT INTO users (name, gender, address, city, mno, emailid, username, password) 
VALUES ('Test User', 'Male', 'Test Address', 'Test City', '1234567890', '<EMAIL>', 'testuser', 'testpass');
```

### Step 6: Browser Issues
1. **Clear Browser Cache:** Ctrl+F5 or Cmd+Shift+R
2. **Check Browser Console:** F12 → Console tab for JavaScript errors
3. **Try Different Browser:** Test with Chrome, Firefox, or Edge

### Step 7: Network/Firewall Issues
1. Check if port 3306 (MySQL) is accessible
2. Verify application server port (8080/8443) is not blocked
3. Test with `telnet localhost 3306` to check MySQL connectivity

## Quick Fixes Checklist

- [ ] MySQL server is running
- [ ] Database `library_management` exists
- [ ] Tables are created (run database_setup.sql)
- [ ] MySQL Connector JAR is in WEB-INF/lib/
- [ ] Database password is correct in DatabaseConfig.java
- [ ] Application is properly deployed
- [ ] No firewall blocking database connection
- [ ] Form method is POST in newuser.html
- [ ] All required form fields are present

## Getting Help

If the issue persists:
1. Run the database test page: `test-db.jsp`
2. Check server logs for detailed error messages
3. Verify all configuration files are correct
4. Test with a simple database connection outside the web application

## Contact Information

For additional support, check:
- Server logs for detailed error messages
- Database connection using command line tools
- Application server documentation for deployment issues
